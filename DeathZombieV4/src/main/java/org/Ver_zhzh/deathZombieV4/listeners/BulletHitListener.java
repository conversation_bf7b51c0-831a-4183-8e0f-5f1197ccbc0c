package org.Ver_zhzh.deathZombieV4.listeners;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.configuration.file.FileConfiguration;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.enums.PlayerStatus;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.Ver_zhzh.deathZombieV4.utils.MessageManager;

/**
 * 子弹命中监听器
 * 处理射击命中时的金钱奖励
 */
public class BulletHitListener implements Listener {

    private final DeathZombieV4 plugin;
    private final GameSessionManager gameSessionManager;
    private final PlayerInteractionManager playerManager;
    private final ShootPluginHelper shootPluginHelper;
    private final MessageManager messageManager;

    public BulletHitListener(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.gameSessionManager = plugin.getGameSessionManager();
        this.playerManager = plugin.getPlayerInteractionManager();
        this.shootPluginHelper = plugin.getShootPluginHelper();
        this.messageManager = plugin.getMessageManager();
    }

    /**
     * 处理实体伤害事件，检测子弹命中
     *
     * @param event 实体伤害事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // 检查是否启用了射击命中奖励模式
        String moneyWay = plugin.getConfig().getString("game.money.getting_money_way", "kill");
        if (!"shoot".equalsIgnoreCase(moneyWay)) {
            return;
        }

        // 检查是否是玩家攻击生物
        if (!(event.getDamager() instanceof Player) || !(event.getEntity() instanceof LivingEntity)) {
            return;
        }

        Player player = (Player) event.getDamager();
        LivingEntity entity = (LivingEntity) event.getEntity();

        // 检查玩家是否在游戏中
        if (!playerManager.isPlayerInStatus(player, PlayerStatus.IN_GAME)) {
            return;
        }

        // 检查实体是否属于游戏
        if (!isGameEntity(entity)) {
            return;
        }

        // 获取玩家所在的游戏
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName == null) {
            return;
        }

        // 计算命中奖励
        double reward = calculateHitReward(player, entity);

        // 给予玩家金钱奖励
        if (reward > 0) {
            String entityType = getEntityType(entity);
            String hitType = determineHitType(player, entity);
            rewardPlayer(player, reward, entityType, hitType);
        }
    }

    /**
     * 检查实体是否属于游戏
     *
     * @param entity 实体
     * @return 是否属于游戏
     */
    private boolean isGameEntity(Entity entity) {
        // 检查实体是否有游戏标记
        return entity.hasMetadata("customZombieType") ||
               entity.hasMetadata("NPC") ||
               entity.hasMetadata("gameEntity") ||
               entity.getCustomName() != null;
    }

    /**
     * 获取实体标识符（优先返回具体ID，否则返回类型）
     *
     * @param entity 实体
     * @return 实体标识符字符串
     */
    private String getEntityType(Entity entity) {
        boolean debugMode = plugin.getConfig().getBoolean("debug", false);

        if (debugMode) {
            plugin.getLogger().info("BulletHitListener.getEntityType: 开始识别实体类型");
            plugin.getLogger().info("实体类型: " + entity.getType());
            plugin.getLogger().info("实体自定义名称: " + entity.getCustomName());
        }

        // 检查是否是自定义NPC
        if (entity.hasMetadata("NPC")) {
            if (debugMode) {
                plugin.getLogger().info("识别为NPC实体");
            }
            return "npc";
        }

        // 检查是否有customZombieType元数据（优先返回具体ID）
        if (entity.hasMetadata("customZombieType")) {
            String zombieType = entity.getMetadata("customZombieType").get(0).asString();
            if (debugMode) {
                plugin.getLogger().info("从customZombieType元数据获取类型: " + zombieType);
            }

            // 清理可能的前缀，确保返回标准格式
            String cleanedType = cleanEntityTypeId(zombieType);
            if (debugMode) {
                plugin.getLogger().info("清理后的类型: " + cleanedType);
            }
            return cleanedType;
        }

        // 检查自定义名称中是否包含ID信息
        if (entity.getCustomName() != null) {
            String name = entity.getCustomName();
            if (debugMode) {
                plugin.getLogger().info("分析自定义名称: " + name);
            }

            // 尝试从名称中提取ID
            if (name.contains("游戏_")) {
                String[] parts = name.split("_");
                if (parts.length >= 4) {
                    // 格式：游戏_<游戏名>_<类型>_<ID>
                    String extractedId = parts[3];
                    if (debugMode) {
                        plugin.getLogger().info("从名称中提取的ID: " + extractedId);
                    }
                    return cleanEntityTypeId(extractedId);
                }
            }

            // 根据名称内容判断类型
            if (name.contains("变异") || name.contains("mutant")) {
                if (debugMode) {
                    plugin.getLogger().info("根据名称识别为变异类型");
                }
                return "mutant";
            } else if (name.contains("特殊") || name.contains("special")) {
                if (debugMode) {
                    plugin.getLogger().info("根据名称识别为特殊类型");
                }
                return "special";
            }
        }

        // 检查其他元数据标记
        if (entity.hasMetadata("idcZombieEntity")) {
            if (debugMode) {
                plugin.getLogger().info("识别为idc类型实体");
            }
            return "zombie"; // 将idc类型实体归类为zombie
        }

        if (entity.hasMetadata("mutantEntity")) {
            if (debugMode) {
                plugin.getLogger().info("识别为变异实体");
            }
            return "mutant";
        }

        // 默认为普通僵尸
        if (debugMode) {
            plugin.getLogger().info("使用默认类型: zombie");
        }
        return "zombie";
    }

    /**
     * 清理实体类型ID，移除不必要的前缀
     *
     * @param rawId 原始ID
     * @return 清理后的ID
     */
    private String cleanEntityTypeId(String rawId) {
        if (rawId == null || rawId.isEmpty()) {
            return "zombie";
        }

        // 移除可能的前缀
        String cleaned = rawId;
        if (cleaned.startsWith("zombie_")) {
            cleaned = cleaned.substring(7); // 移除 "zombie_" 前缀
        } else if (cleaned.startsWith("entity_")) {
            cleaned = cleaned.substring(7); // 移除 "entity_" 前缀
        } else if (cleaned.startsWith("npc_")) {
            cleaned = cleaned.substring(4); // 移除 "npc_" 前缀
        }

        // 确保ID格式正确（id1, idc2, idn3等）
        if (cleaned.matches("\\d+")) {
            // 如果只是数字，默认添加id前缀
            cleaned = "id" + cleaned;
        }

        return cleaned;
    }

    /**
     * 确定命中类型（头部或身体）
     * 使用简化的高度和角度检测算法
     *
     * @param player 玩家
     * @param entity 被命中的实体
     * @return 命中类型
     */
    private String determineHitType(Player player, LivingEntity entity) {
        Location playerEyeLocation = player.getEyeLocation();
        Location entityLocation = entity.getLocation();

        // 获取实体的基本信息
        double entityHeight = entity.getHeight();
        double entityBottom = entityLocation.getY();
        double entityTop = entityBottom + entityHeight;

        // 从配置获取检测参数
        FileConfiguration config = plugin.getConfig();
        double headRegionPercentage = config.getDouble("game.money.head_region_percentage", 0.2);
        boolean debugMode = config.getBoolean("game.money.headshot_debug", false);

        // 计算头部区域的高度范围
        double headHeight = entityHeight * headRegionPercentage;
        double headThreshold = entityTop - headHeight;

        // 计算玩家到实体的水平距离
        double horizontalDistance = Math.sqrt(
            Math.pow(entityLocation.getX() - playerEyeLocation.getX(), 2) +
            Math.pow(entityLocation.getZ() - playerEyeLocation.getZ(), 2)
        );

        // 计算玩家视线的俯仰角度
        double verticalDistance = entityLocation.getY() - playerEyeLocation.getY();
        double pitchAngle = Math.toDegrees(Math.atan2(verticalDistance, horizontalDistance));

        // 获取玩家当前的视线俯仰角
        float playerPitch = playerEyeLocation.getPitch();

        // 计算玩家视线指向的高度（基于角度和距离）
        double targetHeight = playerEyeLocation.getY() + horizontalDistance * Math.tan(Math.toRadians(-playerPitch));

        // 调试信息
        if (debugMode) {
            plugin.getLogger().info("=== 头部命中检测调试 ===");
            plugin.getLogger().info("玩家: " + player.getName());
            plugin.getLogger().info("实体类型: " + entity.getType());
            plugin.getLogger().info("实体高度: " + String.format("%.2f", entityHeight) + ", 底部: " + String.format("%.2f", entityBottom) + ", 顶部: " + String.format("%.2f", entityTop));
            plugin.getLogger().info("头部区域: " + String.format("%.2f", headHeight) + " (占" + (headRegionPercentage * 100) + "%), 阈值: " + String.format("%.2f", headThreshold));
            plugin.getLogger().info("水平距离: " + String.format("%.2f", horizontalDistance));
            plugin.getLogger().info("玩家俯仰角: " + String.format("%.2f", playerPitch));
            plugin.getLogger().info("计算目标高度: " + String.format("%.2f", targetHeight));
            plugin.getLogger().info("实体中心高度: " + String.format("%.2f", entityBottom + entityHeight / 2));
        }

        // 判断命中部位
        String result = "body"; // 默认为身体命中

        // 方法1: 基于目标高度判断
        if (targetHeight >= headThreshold && targetHeight <= entityTop) {
            result = "head";
        }

        // 方法2: 基于玩家俯仰角判断（作为辅助验证）
        // 计算看向实体头部和身体中心所需的角度
        double angleToHead = Math.toDegrees(Math.atan2(entityTop - playerEyeLocation.getY(), horizontalDistance));
        double angleToCenter = Math.toDegrees(Math.atan2((entityBottom + entityHeight / 2) - playerEyeLocation.getY(), horizontalDistance));
        double angleToFeet = Math.toDegrees(Math.atan2(entityBottom - playerEyeLocation.getY(), horizontalDistance));

        // 获取角度容差
        double angleTolerance = config.getDouble("game.money.angle_tolerance", 5.0);

        // 如果玩家视线角度更接近头部，且在合理范围内
        double angleToTarget = Math.toDegrees(Math.atan2(verticalDistance, horizontalDistance));
        double angleDiffToHead = Math.abs((-playerPitch) - angleToHead);
        double angleDiffToCenter = Math.abs((-playerPitch) - angleToCenter);

        // 调试信息
        if (debugMode) {
            plugin.getLogger().info("看向头部角度: " + String.format("%.2f", angleToHead));
            plugin.getLogger().info("看向中心角度: " + String.format("%.2f", angleToCenter));
            plugin.getLogger().info("看向脚部角度: " + String.format("%.2f", angleToFeet));
            plugin.getLogger().info("与头部角度差: " + String.format("%.2f", angleDiffToHead));
            plugin.getLogger().info("与中心角度差: " + String.format("%.2f", angleDiffToCenter));
            plugin.getLogger().info("角度容差: " + angleTolerance);
        }

        // 综合判断：目标高度在头部区域 AND 角度更接近头部
        if (targetHeight >= headThreshold && targetHeight <= entityTop && angleDiffToHead < angleDiffToCenter) {
            result = "head";
        } else {
            result = "body";
        }

        // 调试信息
        if (debugMode) {
            plugin.getLogger().info("最终结果: " + (result.equals("head") ? "头部命中" : "身体命中"));
            plugin.getLogger().info("判断依据: 目标高度=" + String.format("%.2f", targetHeight) +
                                 ", 头部阈值=" + String.format("%.2f", headThreshold) +
                                 ", 角度更接近" + (angleDiffToHead < angleDiffToCenter ? "头部" : "身体"));
        }

        return result;
    }

    /**
     * 计算命中奖励金额
     *
     * @param player 玩家
     * @param entity 被命中的实体
     * @return 奖励金额
     */
    private double calculateHitReward(Player player, LivingEntity entity) {
        FileConfiguration config = plugin.getConfig();
        double baseReward = 0;

        String entityType = getEntityType(entity);

        // 检查是否启用枪支特定奖励
        boolean weaponSpecificEnabled = config.getBoolean("game.money.weapon_specific_rewards.enabled", false);

        if (weaponSpecificEnabled) {
            // 获取玩家当前使用的枪支ID
            String gunId = shootPluginHelper.getPlayerCurrentGunId(player);

            if (gunId != null) {
                // 使用枪支特定的奖励配置
                baseReward = getWeaponSpecificShootReward(gunId, entityType);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("BulletHitListener: 使用枪支特定奖励 - 枪支ID: " + gunId +
                        ", 实体类型: " + entityType + ", 基础奖励: " + baseReward);
                }
            } else {
                // 如果无法获取枪支ID，使用全局配置
                baseReward = getGlobalShootReward(entityType);

                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("BulletHitListener: 无法获取枪支ID，使用全局奖励配置");
                }
            }
        } else {
            // 使用全局配置
            baseReward = getGlobalShootReward(entityType);
        }

        // 根据命中部位计算倍数
        String hitType = determineHitType(player, entity);
        double multiplier = 1.0;

        if ("head".equals(hitType)) {
            multiplier = config.getDouble("game.money.headshot_multiplier", 2.0);
        } else {
            multiplier = config.getDouble("game.money.bodyshot_multiplier", 1.0);
        }

        // 计算最终奖励
        double finalReward = baseReward * multiplier;

        // 应用最小/最大限制
        double minReward = config.getDouble("game.money.min_reward", 1);
        double maxReward = config.getDouble("game.money.max_reward", 100);

        finalReward = Math.max(minReward, Math.min(finalReward, maxReward));

        // 四舍五入到整数
        return Math.round(finalReward);
    }

    /**
     * 获取全局射击奖励配置
     *
     * @param entityType 实体类型
     * @return 基础奖励金额
     */
    private double getGlobalShootReward(String entityType) {
        FileConfiguration config = plugin.getConfig();

        switch (entityType) {
            case "npc":
                return config.getDouble("game.money.npc_hit", 10);
            case "mutant":
                return config.getDouble("game.money.mutant_hit", 6);
            case "zombie":
                return config.getDouble("game.money.zombie_hit", 2);
            default:
                return config.getDouble("game.money.monster_hit", 3);
        }
    }

    /**
     * 获取枪支特定的射击奖励配置
     *
     * @param gunId 枪支ID
     * @param entityType 实体类型
     * @return 基础奖励金额
     */
    private double getWeaponSpecificShootReward(String gunId, String entityType) {
        FileConfiguration config = plugin.getConfig();
        String basePath = "game.money.weapon_specific_rewards.weapons." + gunId + ".shoot.";

        switch (entityType) {
            case "npc":
                return config.getDouble(basePath + "npc_hit", getGlobalShootReward(entityType));
            case "mutant":
                return config.getDouble(basePath + "mutant_hit", getGlobalShootReward(entityType));
            case "zombie":
                return config.getDouble(basePath + "zombie_hit", getGlobalShootReward(entityType));
            default:
                return config.getDouble(basePath + "monster_hit", getGlobalShootReward(entityType));
        }
    }

    /**
     * 给玩家奖励金钱
     *
     * @param player 玩家
     * @param amount 金额
     * @param entityType 实体类型
     * @param hitType 命中类型
     */
    private void rewardPlayer(Player player, double amount, String entityType, String hitType) {
        // 使用ShootPluginHelper给玩家加钱
        shootPluginHelper.addPlayerMoney(player, amount);

        // 记录统计数据
        String gameName = gameSessionManager.getPlayerGame(player);
        if (gameName != null) {
            plugin.getPlayerInteractionManager().addPlayerMoneyEarned(player, gameName, amount);
        }

        // 使用MessageManager发送消息
        messageManager.sendShootingMessage(player, hitType, entityType, (int) amount);

        // 调试信息
        if (plugin.getConfig().getBoolean("debug", false)) {
            String hitTypeText = messageManager.getHitTypeDisplayName(hitType);
            String entityTypeText = messageManager.getEntityDisplayName(entityType);
            plugin.getLogger().info("BulletHitListener: 玩家 " + player.getName() +
                " " + hitTypeText + " " + entityTypeText + " 获得 " + amount + " 金钱");
        }
    }


}
