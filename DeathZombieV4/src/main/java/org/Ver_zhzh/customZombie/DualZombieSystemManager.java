package org.Ver_zhzh.customZombie;

import java.util.logging.Logger;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.entity.Zombie;
import org.bukkit.plugin.Plugin;
import org.Ver_zhzh.customZombie.UserCustomZombie.UserCustomZombie;

/**
 * 双CustomZombie系统管理器
 * 协调原有CustomZombie和新的UserCustomZombie系统
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
public class DualZombieSystemManager {
    
    // 插件实例和日志记录器
    private final Plugin plugin;
    private final Logger logger;
    
    // 两个僵尸系统的实例
    private final CustomZombie originalCustomZombie;
    private final UserCustomZombie userCustomZombie;
    
    // 系统状态
    private boolean initialized = false;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param originalCustomZombie 原有CustomZombie实例
     */
    public DualZombieSystemManager(Plugin plugin, CustomZombie originalCustomZombie) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.originalCustomZombie = originalCustomZombie;
        
        // 初始化UserCustomZombie系统
        this.userCustomZombie = new UserCustomZombie(plugin, originalCustomZombie);
        
        this.initialized = true;
        logger.info("双CustomZombie系统管理器初始化完成");
    }
    
    /**
     * 生成自定义僵尸（统一入口）
     * 根据配置决定使用哪个系统
     * 
     * @param player 触发生成的玩家
     * @param zombieId 僵尸ID
     * @return 生成的僵尸实体，失败时返回null
     */
    public Zombie spawnCustomZombie(Player player, String zombieId) {
        if (!initialized) {
            logger.severe("双僵尸系统未初始化");
            return null;
        }
        
        Location spawnLocation = player.getLocation().add(player.getLocation().getDirection().multiply(2));
        return spawnCustomZombieDirect(spawnLocation, zombieId);
    }
    
    /**
     * 直接在指定位置生成自定义僵尸
     * 
     * @param location 生成位置
     * @param zombieId 僵尸ID
     * @return 生成的僵尸实体，失败时返回null
     */
    public Zombie spawnCustomZombieDirect(Location location, String zombieId) {
        if (!initialized) {
            logger.severe("双僵尸系统未初始化");
            return null;
        }
        
        // 检查是否为支持的ID（目前支持ID1-ID25）
        if (!isSupportedZombieId(zombieId)) {
            logger.info("僵尸ID " + zombieId + " 不在双系统支持范围内，使用原有系统");
            return originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
        }
        
        logger.info("尝试使用双系统生成僵尸: " + zombieId);
        
        // 根据UserCustomZombie的配置决定生成策略
        String strategy = userCustomZombie.getPriorityStrategy();
        boolean userCustomEnabled = userCustomZombie.isUserCustomEnabled();
        boolean defaultEnabled = userCustomZombie.isDefaultEnabled();
        
        switch (strategy) {
            case "user_first":
                // 优先使用用户自定义系统
                if (userCustomEnabled) {
                    Zombie userZombie = userCustomZombie.spawnUserCustomZombieDirect(location, zombieId);
                    if (userZombie != null) {
                        logger.info("使用用户自定义系统成功生成: " + zombieId);
                        return userZombie;
                    }
                    logger.warning("用户自定义系统生成失败，尝试默认系统");
                }

                if (defaultEnabled) {
                    Zombie zombie = originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
                    if (zombie != null) {
                        logger.info("使用默认系统成功生成: " + zombieId);
                        return zombie;
                    }
                }
                break;
                
            case "default_first":
                // 优先使用默认系统
                if (defaultEnabled) {
                    Zombie zombie = originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
                    if (zombie != null) {
                        logger.info("使用默认系统成功生成: " + zombieId);
                        return zombie;
                    }
                    logger.warning("默认系统生成失败，尝试用户自定义系统");
                }
                
                if (userCustomEnabled) {
                    Zombie userZombie = userCustomZombie.spawnUserCustomZombieDirect(location, zombieId);
                    if (userZombie != null) {
                        logger.info("使用用户自定义系统成功生成: " + zombieId);
                        return userZombie;
                    }
                }
                break;
                
            case "both":
                // 同时使用两个系统（实验性功能）
                Zombie defaultZombie = null;
                Zombie userZombie = null;

                if (defaultEnabled) {
                    defaultZombie = originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
                }

                if (userCustomEnabled) {
                    // 在稍微偏移的位置生成用户自定义僵尸
                    Location offsetLocation = location.clone().add(1, 0, 1);
                    userZombie = userCustomZombie.spawnUserCustomZombieDirect(offsetLocation, zombieId);
                }

                if (defaultZombie != null || userZombie != null) {
                    logger.info("双系统生成完成: 默认=" + (defaultZombie != null) + ", 用户自定义=" + (userZombie != null));
                    // 优先返回用户自定义僵尸
                    return userZombie != null ? userZombie : defaultZombie;
                }
                break;
                
            default:
                logger.warning("未知的优先级策略: " + strategy + "，使用默认系统");
                if (defaultEnabled) {
                    return originalCustomZombie.spawnCustomZombieDirect(location, zombieId);
                }
                break;
        }
        
        logger.warning("所有系统都无法生成僵尸: " + zombieId);
        return null;
    }
    
    /**
     * 检查是否为支持的僵尸ID
     *
     * @param zombieId 僵尸ID
     * @return 是否支持
     */
    private boolean isSupportedZombieId(String zombieId) {
        // 目前支持ID1-ID25
        return zombieId.equals("id1") || zombieId.equals("id2") ||
               zombieId.equals("id3") || zombieId.equals("id4") ||
               zombieId.equals("id5") || zombieId.equals("id6") ||
               zombieId.equals("id7") || zombieId.equals("id8") ||
               zombieId.equals("id9") || zombieId.equals("id10") ||
               zombieId.equals("id11") || zombieId.equals("id12") ||
               zombieId.equals("id13") || zombieId.equals("id14") ||
               zombieId.equals("id15") || zombieId.equals("id16") ||
               zombieId.equals("id17") || zombieId.equals("id18") ||
               zombieId.equals("id19") || zombieId.equals("id20") ||
               zombieId.equals("id21") || zombieId.equals("id22") ||
               zombieId.equals("id23") || zombieId.equals("id24") ||
               zombieId.equals("id25");
    }
    
    /**
     * 重载配置
     */
    public void reloadConfig() {
        if (userCustomZombie != null) {
            userCustomZombie.reloadConfig();
            logger.info("双僵尸系统配置重载完成");
        }
    }
    
    /**
     * 获取原有CustomZombie实例
     * 
     * @return CustomZombie实例
     */
    public CustomZombie getOriginalCustomZombie() {
        return originalCustomZombie;
    }
    
    /**
     * 获取UserCustomZombie实例
     * 
     * @return UserCustomZombie实例
     */
    public UserCustomZombie getUserCustomZombie() {
        return userCustomZombie;
    }
    
    /**
     * 检查系统是否已初始化
     * 
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 获取系统状态信息
     * 
     * @return 状态信息字符串
     */
    public String getSystemStatus() {
        if (!initialized) {
            return "§c双僵尸系统未初始化";
        }
        
        StringBuilder status = new StringBuilder();
        status.append("§a双僵尸系统状态:\n");
        status.append("§7- 默认系统启用: ").append(userCustomZombie.isDefaultEnabled() ? "§a是" : "§c否").append("\n");
        status.append("§7- 用户自定义启用: ").append(userCustomZombie.isUserCustomEnabled() ? "§a是" : "§c否").append("\n");
        status.append("§7- 优先级策略: §e").append(userCustomZombie.getPriorityStrategy()).append("\n");
        status.append("§7- 支持的僵尸ID: §eID1-ID7");
        
        return status.toString();
    }
}
