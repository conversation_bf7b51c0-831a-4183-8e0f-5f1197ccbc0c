package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * 双实体系统管理器
 * 管理原有IDC系统和新的UserCustomEntity系统之间的协调
 *
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class DualEntitySystemManager {

    private final Plugin plugin;
    private final Logger logger;
    private final UserCustomEntity userCustomEntity;
    private final Object originalEntitySpawner;
    private boolean initialized = false;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param userCustomEntity UserCustomEntity实例
     * @param originalEntitySpawner 原有的实体生成器
     */
    public DualEntitySystemManager(Plugin plugin, UserCustomEntity userCustomEntity, Object originalEntitySpawner) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.userCustomEntity = userCustomEntity;
        this.originalEntitySpawner = originalEntitySpawner;
        this.initialized = true;

        logger.info("双实体系统管理器初始化完成");
    }

    /**
     * 直接在指定位置生成自定义实体
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 生成的实体，失败时返回null
     */
    public LivingEntity spawnCustomEntityDirect(Location location, String entityId) {
        if (!initialized) {
            logger.severe("双实体系统未初始化");
            return null;
        }

        // 检查是否为支持的ID（目前支持IDC1-IDC25）
        if (!isSupportedEntityId(entityId)) {
            logger.info("实体ID " + entityId + " 不在双系统支持范围内，使用原有系统");
            return spawnWithOriginalSystem(location, entityId);
        }

        logger.info("尝试使用双系统生成实体: " + entityId);

        // 根据UserCustomEntity的配置决定生成策略
        String strategy = userCustomEntity.getPriorityStrategy();
        boolean userCustomEnabled = userCustomEntity.isUserCustomEnabled();
        boolean defaultEnabled = userCustomEntity.isDefaultEnabled();

        switch (strategy) {
            case "user_first":
                // 优先使用用户自定义系统
                if (userCustomEnabled) {
                    LivingEntity userEntity = userCustomEntity.spawnUserCustomEntityDirect(location, entityId);
                    if (userEntity != null) {
                        logger.info("使用用户自定义系统成功生成: " + entityId);
                        return userEntity;
                    }
                    logger.warning("用户自定义系统生成失败，尝试默认系统");
                }

                if (defaultEnabled) {
                    LivingEntity entity = spawnWithOriginalSystem(location, entityId);
                    if (entity != null) {
                        logger.info("使用默认系统成功生成: " + entityId);
                        return entity;
                    }
                }
                break;

            case "default_first":
                // 优先使用默认系统
                if (defaultEnabled) {
                    LivingEntity entity = spawnWithOriginalSystem(location, entityId);
                    if (entity != null) {
                        logger.info("使用默认系统成功生成: " + entityId);
                        return entity;
                    }
                    logger.warning("默认系统生成失败，尝试用户自定义系统");
                }

                if (userCustomEnabled) {
                    LivingEntity userEntity = userCustomEntity.spawnUserCustomEntityDirect(location, entityId);
                    if (userEntity != null) {
                        logger.info("使用用户自定义系统成功生成: " + entityId);
                        return userEntity;
                    }
                }
                break;

            case "both":
                // 同时使用两个系统（实验性功能）
                LivingEntity defaultEntity = null;
                LivingEntity userEntity = null;

                if (defaultEnabled) {
                    defaultEntity = spawnWithOriginalSystem(location, entityId);
                }

                if (userCustomEnabled) {
                    // 在稍微偏移的位置生成用户自定义实体
                    Location offsetLocation = location.clone().add(1, 0, 1);
                    userEntity = userCustomEntity.spawnUserCustomEntityDirect(offsetLocation, entityId);
                }

                if (defaultEntity != null || userEntity != null) {
                    logger.info("双系统生成完成: 默认=" + (defaultEntity != null) + ", 用户自定义=" + (userEntity != null));
                    // 优先返回用户自定义实体
                    return userEntity != null ? userEntity : defaultEntity;
                }
                break;

            default:
                logger.warning("未知的优先级策略: " + strategy + "，使用用户自定义");
                return userCustomEntity.spawnUserCustomEntityDirect(location, entityId);
        }

        logger.warning("所有生成方式都失败了: " + entityId);
        return null;
    }

    /**
     * 使用原有系统生成实体
     */
    private LivingEntity spawnWithOriginalSystem(Location location, String entityId) {
        try {
            // 这里需要调用原有的AdvancedEntitySpawner或ZombieHelper
            // 暂时返回null，等待集成
            if (originalEntitySpawner != null) {
                // 通过反射调用原有系统的生成方法
                // 具体实现需要根据原有系统的接口来定
                logger.info("调用原有系统生成实体: " + entityId);
            }
            return null;
        } catch (Exception e) {
            logger.warning("原有系统生成实体失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 检查实体ID是否被支持
     */
    private boolean isSupportedEntityId(String entityId) {
        return userCustomEntity.isSupportedEntityId(entityId);
    }

    /**
     * 获取系统状态信息
     */
    public String getSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("双实体系统状态:\n");
        status.append("- 初始化状态: ").append(initialized).append("\n");
        status.append("- 用户自定义系统启用: ").append(userCustomEntity.isUserCustomEnabled()).append("\n");
        status.append("- 默认系统启用: ").append(userCustomEntity.isDefaultEnabled()).append("\n");
        status.append("- 优先级策略: ").append(userCustomEntity.getPriorityStrategy()).append("\n");
        status.append("- 调试模式: ").append(userCustomEntity.isDebugMode()).append("\n");
        status.append("- 已加载配置数量: ").append(userCustomEntity.getConfigCache().size());
        return status.toString();
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        userCustomEntity.reloadConfig();
        logger.info("双实体系统配置已重载");
    }

    /**
     * 检查系统是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
}
