package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * UserCustomEntity系统集成类
 * 负责将新的UserCustomEntity系统集成到现有的DeathZombieV4插件中
 * 
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class UserCustomEntityIntegration {
    
    private final Plugin plugin;
    private final Logger logger;
    private UserCustomEntity userCustomEntity;
    private DualEntitySystemManager dualSystemManager;
    private UserCustomEntityTest testSystem;
    private boolean initialized = false;
    
    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public UserCustomEntityIntegration(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
    }
    
    /**
     * 初始化UserCustomEntity系统
     *
     * @param originalEntitySpawner 原有的实体生成器实例
     * @return 是否初始化成功
     */
    public boolean initialize(Object originalEntitySpawner) {
        try {
            logger.info("开始初始化UserCustomEntity系统...");
            
            // 初始化UserCustomEntity
            userCustomEntity = new UserCustomEntity(plugin, originalEntitySpawner);
            
            // 初始化双系统管理器
            dualSystemManager = new DualEntitySystemManager(plugin, userCustomEntity, originalEntitySpawner);
            
            // 初始化测试系统
            testSystem = new UserCustomEntityTest(plugin);
            
            initialized = true;
            logger.info("UserCustomEntity系统初始化成功");
            
            // 输出系统状态
            logger.info(dualSystemManager.getSystemStatus());
            
            return true;
            
        } catch (Exception e) {
            logger.severe("UserCustomEntity系统初始化失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 生成自定义实体（主要接口）
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 生成的实体，失败时返回null
     */
    public LivingEntity spawnCustomEntity(Location location, String entityId) {
        if (!initialized) {
            logger.warning("UserCustomEntity系统未初始化，无法生成实体: " + entityId);
            return null;
        }
        
        if (!isSupportedEntity(entityId)) {
            logger.info("实体ID " + entityId + " 不被UserCustomEntity系统支持");
            return null;
        }
        
        try {
            return dualSystemManager.spawnCustomEntityDirect(location, entityId);
        } catch (Exception e) {
            logger.severe("生成自定义实体失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 检查实体是否被支持
     */
    public boolean isSupportedEntity(String entityId) {
        if (!initialized || userCustomEntity == null) {
            return false;
        }
        return userCustomEntity.isSupportedEntityId(entityId);
    }
    
    /**
     * 重载配置
     */
    public void reloadConfig() {
        if (!initialized) {
            logger.warning("系统未初始化，无法重载配置");
            return;
        }
        
        try {
            dualSystemManager.reloadConfig();
            logger.info("UserCustomEntity配置重载完成");
        } catch (Exception e) {
            logger.severe("配置重载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取系统状态
     */
    public String getSystemStatus() {
        if (!initialized) {
            return "UserCustomEntity系统未初始化";
        }
        return dualSystemManager.getSystemStatus();
    }
    
    /**
     * 运行系统测试
     */
    public void runSystemTest(Location testLocation) {
        if (!initialized) {
            logger.warning("系统未初始化，无法运行测试");
            return;
        }
        
        if (testLocation == null) {
            logger.warning("测试位置为null，无法运行测试");
            return;
        }
        
        try {
            testSystem.runAllTests(testLocation);
        } catch (Exception e) {
            logger.severe("运行系统测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取支持的实体列表
     */
    public String[] getSupportedEntities() {
        // 目前只支持idc1，后续会添加更多
        return new String[]{"idc1"};
    }
    
    /**
     * 检查系统是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 获取UserCustomEntity实例
     */
    public UserCustomEntity getUserCustomEntity() {
        return userCustomEntity;
    }
    
    /**
     * 获取双系统管理器实例
     */
    public DualEntitySystemManager getDualSystemManager() {
        return dualSystemManager;
    }
    
    /**
     * 关闭系统
     */
    public void shutdown() {
        if (initialized) {
            logger.info("正在关闭UserCustomEntity系统...");
            
            // 清理资源
            userCustomEntity = null;
            dualSystemManager = null;
            testSystem = null;
            initialized = false;
            
            logger.info("UserCustomEntity系统已关闭");
        }
    }
    
    /**
     * 获取实体配置信息
     */
    public String getEntityConfigInfo(String entityId) {
        if (!initialized || !isSupportedEntity(entityId)) {
            return "实体 " + entityId + " 不被支持或系统未初始化";
        }
        
        try {
            EntityOverrideConfig config = userCustomEntity.getConfigCache().get(entityId);
            if (config == null) {
                return "实体 " + entityId + " 没有自定义配置";
            }
            
            StringBuilder info = new StringBuilder();
            info.append("实体 ").append(entityId).append(" 配置信息:\n");
            info.append("- 启用状态: ").append(config.enabled).append("\n");
            info.append("- 生命值覆盖: ").append(config.healthOverride).append("\n");
            info.append("- 伤害覆盖: ").append(config.damageOverride).append("\n");
            info.append("- 速度倍数: ").append(config.speedMultiplier).append("\n");
            info.append("- 自定义名称: ").append(config.customNameOverride).append("\n");
            info.append("- 实体类型: ").append(config.entityTypeOverride).append("\n");
            info.append("- 武器覆盖: ").append(config.weaponOverride).append("\n");
            info.append("- 特殊能力数量: ").append(config.specialAbilities.size()).append("\n");
            info.append("- 技能冷却覆盖数量: ").append(config.skillCooldownOverrides.size());
            
            return info.toString();
            
        } catch (Exception e) {
            return "获取配置信息失败: " + e.getMessage();
        }
    }
    
    /**
     * 测试特定实体的生成
     */
    public boolean testEntitySpawn(Location location, String entityId) {
        if (!initialized) {
            logger.warning("系统未初始化，无法测试实体生成");
            return false;
        }
        
        if (!isSupportedEntity(entityId)) {
            logger.warning("实体 " + entityId + " 不被支持");
            return false;
        }
        
        try {
            LivingEntity entity = spawnCustomEntity(location, entityId);
            if (entity != null) {
                logger.info("实体 " + entityId + " 生成测试成功");
                return true;
            } else {
                logger.warning("实体 " + entityId + " 生成测试失败");
                return false;
            }
        } catch (Exception e) {
            logger.severe("测试实体生成时发生异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
