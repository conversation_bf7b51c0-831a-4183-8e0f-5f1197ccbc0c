package org.Ver_zhzh.customZombie.UserCustomEntity;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.PigZombie;
import org.bukkit.entity.Zombie;
import org.bukkit.entity.Blaze;
import org.bukkit.entity.SmallFireball;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Creeper;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.LeatherArmorMeta;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.Sound;
import org.bukkit.Color;
import org.bukkit.Particle;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.Bukkit;
import org.bukkit.boss.BossBar;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import java.util.Set;
import java.util.HashSet;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.entity.Player;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.entity.Arrow;
import org.bukkit.util.Vector;
import org.bukkit.GameMode;
import org.bukkit.block.Block;
import org.bukkit.DyeColor;
import org.bukkit.World;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import org.Ver_zhzh.customZombie.ParticleHelper;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 用户自定义实体系统
 * 提供IDC实体的自定义配置和生成功能
 *
 * <AUTHOR>
 * @version 1.0
 * 符合阿里巴巴编码规范
 */
public class UserCustomEntity implements Listener {

    // 插件实例和日志记录器
    private final Plugin plugin;
    private final Logger logger;

    // 原有AdvancedEntitySpawner实例的引用，用于技能共享
    private final Object originalEntitySpawner;

    // 高级技能处理器
    private final AdvancedEntitySkillHandler advancedSkillHandler;

    // 粒子效果助手
    private ParticleHelper particleHelper;

    // 粒子效果任务管理
    private final Map<LivingEntity, BukkitTask> particleEffectTasks = new HashMap<>();

    // 配置文件相关
    private FileConfiguration entityConfig;
    private File entityConfigFile;

    // 配置缓存
    private final Map<String, EntityOverrideConfig> configCache = new HashMap<>();

    // 系统设置缓存
    private boolean useDefaultSettings = true;
    private boolean useUserCustomSettings = false;
    private String priorityStrategy = "default_first";
    private boolean debugMode = false;

    // 性能配置缓存
    private int maxUserCustomEntities = 50;
    private int skillProcessingInterval = 20;
    private int entitySpawnCooldown = 100;
    private boolean enableConfigCache = true;
    private int configReloadInterval = 300;

    // 调试配置缓存
    private boolean logEntitySpawn = true;
    private boolean logConfigLoading = true;
    private boolean logSkillExecution = true;
    private boolean logErrorDetails = true;
    private String logLevel = "INFO";
    private boolean enablePerformanceMonitoring = false;
    private int performanceMonitorInterval = 60;

    // 兼容性配置缓存
    private boolean legacyCompatibility = true;
    private boolean preserveOriginalMetadata = true;
    private boolean preserveOriginalNaming = true;
    private boolean enableEntityTypeFallback = true;
    private boolean enableVersionCheck = true;
    private String minBukkitVersion = "1.16";

    // 实体计数器
    private int currentUserCustomEntityCount = 0;
    private long lastSpawnTime = 0;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param originalEntitySpawner 原有的实体生成器实例
     */
    public UserCustomEntity(Plugin plugin, Object originalEntitySpawner) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.originalEntitySpawner = originalEntitySpawner;
        this.advancedSkillHandler = new AdvancedEntitySkillHandler(plugin, this);

        // 初始化粒子效果助手
        this.particleHelper = new ParticleHelper(plugin);

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);

        // 初始化配置文件
        initializeConfig();

        // 加载配置
        loadConfig();

        logger.info("UserCustomEntity系统初始化完成");
    }

    /**
     * 初始化配置文件
     */
    private void initializeConfig() {
        entityConfigFile = new File(plugin.getDataFolder(), "entity.yml");

        if (!entityConfigFile.exists()) {
            plugin.saveResource("entity.yml", false);
            logger.info("已创建默认entity.yml配置文件");
        }
    }

    /**
     * 加载配置文件
     */
    public void loadConfig() {
        try {
            entityConfig = YamlConfiguration.loadConfiguration(entityConfigFile);

            // 加载系统设置
            ConfigurationSection systemSettings = entityConfig.getConfigurationSection("system_settings");
            if (systemSettings != null) {
                useDefaultSettings = systemSettings.getBoolean("use_default_settings", true);
                useUserCustomSettings = systemSettings.getBoolean("use_user_custom_settings", false);
                priorityStrategy = systemSettings.getString("priority_strategy", "default_first");
                debugMode = systemSettings.getBoolean("debug_mode", false);
            }

            // 加载性能配置
            ConfigurationSection performanceSettings = entityConfig.getConfigurationSection("performance");
            if (performanceSettings != null) {
                maxUserCustomEntities = performanceSettings.getInt("max_user_custom_entities", 50);
                skillProcessingInterval = performanceSettings.getInt("skill_processing_interval", 20);
                entitySpawnCooldown = performanceSettings.getInt("entity_spawn_cooldown", 100);
                enableConfigCache = performanceSettings.getBoolean("enable_config_cache", true);
                configReloadInterval = performanceSettings.getInt("config_reload_interval", 300);
            }

            // 加载调试配置
            ConfigurationSection debugSettings = entityConfig.getConfigurationSection("debug");
            if (debugSettings != null) {
                logEntitySpawn = debugSettings.getBoolean("log_entity_spawn", true);
                logConfigLoading = debugSettings.getBoolean("log_config_loading", true);
                logSkillExecution = debugSettings.getBoolean("log_skill_execution", true);
                logErrorDetails = debugSettings.getBoolean("log_error_details", true);
                logLevel = debugSettings.getString("log_level", "INFO");
                enablePerformanceMonitoring = debugSettings.getBoolean("enable_performance_monitoring", false);
                performanceMonitorInterval = debugSettings.getInt("performance_monitor_interval", 60);
            }

            // 加载兼容性配置
            ConfigurationSection compatibilitySettings = entityConfig.getConfigurationSection("compatibility");
            if (compatibilitySettings != null) {
                legacyCompatibility = compatibilitySettings.getBoolean("legacy_compatibility", true);
                preserveOriginalMetadata = compatibilitySettings.getBoolean("preserve_original_metadata", true);
                preserveOriginalNaming = compatibilitySettings.getBoolean("preserve_original_naming", true);
                enableEntityTypeFallback = compatibilitySettings.getBoolean("enable_entity_type_fallback", true);
                enableVersionCheck = compatibilitySettings.getBoolean("enable_version_check", true);
                minBukkitVersion = compatibilitySettings.getString("min_bukkit_version", "1.16");
            }

            // 清空并重新加载配置缓存
            configCache.clear();
            loadOverrideConfigs();

            if (debugMode && logConfigLoading) {
                logger.info("UserCustomEntity配置加载完成:");
                logger.info("- 使用默认设置: " + useDefaultSettings);
                logger.info("- 使用用户自定义设置: " + useUserCustomSettings);
                logger.info("- 优先级策略: " + priorityStrategy);
                logger.info("- 已加载覆盖配置数量: " + configCache.size());
                logger.info("- 最大实体数量: " + maxUserCustomEntities);
                logger.info("- 技能处理间隔: " + skillProcessingInterval + "tick");
                logger.info("- 实体生成冷却: " + entitySpawnCooldown + "ms");
                logger.info("- 配置缓存启用: " + enableConfigCache);
                logger.info("- 兼容性模式: " + legacyCompatibility);
            }

        } catch (Exception e) {
            logger.severe("加载entity.yml配置文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载覆盖配置
     */
    private void loadOverrideConfigs() {
        ConfigurationSection overrides = entityConfig.getConfigurationSection("user_custom_overrides.specific_overrides");
        if (overrides == null) {
            logger.warning("未找到specific_overrides配置节");
            return;
        }

        // 加载IDC1-IDC25的配置
        for (String entityId : new String[]{"idc1", "idc2", "idc3", "idc4", "idc5", "idc6", "idc7", "idc8", "idc9", "idc10",
                                           "idc11", "idc12", "idc13", "idc14", "idc15", "idc16", "idc17", "idc18", "idc19", "idc20",
                                           "idc21", "idc22", "idc23", "idc24", "idc25"}) {
            ConfigurationSection entitySection = overrides.getConfigurationSection(entityId);
            if (entitySection != null && entitySection.getBoolean("enabled", false)) {
                EntityOverrideConfig config = new EntityOverrideConfig();
                config.loadFromConfig(entitySection);
                configCache.put(entityId, config);

                if (debugMode) {
                    logger.info("已加载" + entityId + "的覆盖配置");
                }
            }
        }
    }

    /**
     * 直接生成用户自定义实体
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @return 生成的实体，失败时返回null
     */
    public LivingEntity spawnUserCustomEntityDirect(Location location, String entityId) {
        if (!useUserCustomSettings) {
            if (debugMode && logEntitySpawn) {
                logger.info("用户自定义实体系统已禁用，跳过生成: " + entityId);
            }
            return null;
        }

        // 检查实体数量限制
        if (currentUserCustomEntityCount >= maxUserCustomEntities) {
            if (debugMode && logEntitySpawn) {
                logger.warning("已达到最大用户自定义实体数量限制: " + maxUserCustomEntities);
            }
            return null;
        }

        // 检查生成冷却时间
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSpawnTime < entitySpawnCooldown) {
            if (debugMode && logEntitySpawn) {
                logger.info("实体生成冷却中，剩余: " + (entitySpawnCooldown - (currentTime - lastSpawnTime)) + "ms");
            }
            return null;
        }

        // 获取覆盖配置
        EntityOverrideConfig overrideConfig = configCache.get(entityId);
        if (overrideConfig == null) {
            if (debugMode && logConfigLoading) {
                logger.info("未找到" + entityId + "的覆盖配置，使用默认设置");
            }
            // 创建默认配置
            overrideConfig = createDefaultConfig(entityId);
        }

        if (debugMode && logEntitySpawn) {
            logger.info("开始生成用户自定义实体: " + entityId + " (当前数量: " + currentUserCustomEntityCount + "/" + maxUserCustomEntities + ")");
        }

        // 根据优先级策略决定生成方式
        switch (priorityStrategy) {
            case "user_first":
                return spawnWithUserCustomSettings(location, entityId, overrideConfig);

            case "default_first":
                // 先尝试默认设置，失败时使用用户自定义
                if (useDefaultSettings && originalEntitySpawner != null) {
                    try {
                        LivingEntity defaultEntity = spawnWithDefaultSettings(location, entityId);
                        if (defaultEntity != null) {
                            // 应用用户自定义覆盖到默认实体
                            applyOverridesToEntity(defaultEntity, overrideConfig);
                            return defaultEntity;
                        }
                    } catch (Exception e) {
                        logger.warning("默认实体生成失败，尝试用户自定义: " + e.getMessage());
                    }
                }
                return spawnWithUserCustomSettings(location, entityId, overrideConfig);

            case "both":
                // 同时使用两套系统（实验性功能）
                LivingEntity userEntity = spawnWithUserCustomSettings(location, entityId, overrideConfig);
                if (userEntity != null && useDefaultSettings) {
                    // 在稍微偏移的位置生成默认实体
                    Location offsetLocation = location.clone().add(1, 0, 1);
                    spawnWithDefaultSettings(offsetLocation, entityId);
                }
                return userEntity;

            default:
                logger.warning("未知的优先级策略: " + priorityStrategy + "，使用用户自定义");
                return spawnWithUserCustomSettings(location, entityId, overrideConfig);
        }
    }

    /**
     * 使用用户自定义设置生成实体
     *
     * @param location 生成位置
     * @param entityId 实体ID
     * @param overrideConfig 覆盖配置
     * @return 生成的实体，失败时返回null
     */
    private LivingEntity spawnWithUserCustomSettings(Location location, String entityId, EntityOverrideConfig overrideConfig) {
        try {
            // 根据entityId生成对应的实体
            LivingEntity entity = spawnSpecificEntity(location, entityId, overrideConfig);
            if (entity == null) {
                return null;
            }

            // 应用覆盖配置
            applyOverridesToEntity(entity, overrideConfig);

            // 添加用户自定义标记
            entity.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            entity.setMetadata("entityId", new FixedMetadataValue(plugin, entityId));
            entity.setMetadata("dualEntitySystem", new FixedMetadataValue(plugin, "user_custom"));
            entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            entity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

            // 启用特殊技能
            enableEntitySkills(entity, entityId, overrideConfig);

            // 更新计数器和时间戳
            currentUserCustomEntityCount++;
            lastSpawnTime = System.currentTimeMillis();

            if (debugMode && logEntitySpawn) {
                logger.info("成功生成用户自定义实体: " + entityId + " (当前数量: " + currentUserCustomEntityCount + "/" + maxUserCustomEntities + ")");
            }

            return entity;

        } catch (Exception e) {
            logger.severe("使用用户自定义设置生成实体失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成特定的实体（专门处理idc1、idc2、idc3、idc4、idc5等）
     */
    private LivingEntity spawnSpecificEntity(Location location, String entityId, EntityOverrideConfig config) {
        switch (entityId) {
            case "idc1": // 变异僵尸01
                return spawnMutantZombie01(location, config);
            case "idc2": // 变异僵尸02
                return spawnMutantZombie02(location, config);
            case "idc3": // 变异烈焰人
                return spawnMutantBlaze(location, config);
            case "idc4": // 变异爬行者
                return spawnMutantCreeper(location, config);
            case "idc5": // 变异末影螨
                return spawnMutantEndermite(location, config);
            case "idc6": // 变异蜘蛛
                return spawnMutantSpider(location, config);
            case "idc7": // 灾厄卫道士
                return spawnDisasterGuardian(location, config);
            case "idc8": // 灾厄唤魔者
                return spawnDisasterSummoner(location, config);
            case "idc9": // 灾厄劫掠兽
                return spawnDisasterRavagerBeast(location, config);
            case "idc10": // 变异僵尸马
                return spawnMutantZombieHorse(location, config);
            case "idc11": // 变异岩浆怪
                return spawnMutantMagmaCube(location, config);
            case "idc12": // 变异尸壳
                return spawnMutantHusk(location, config);
            case "idc13": // 变异僵尸3
                return spawnSwampStray(location, config);
            case "idc14": // 变异僵尸4
                return spawnMutantZombie04(location, config);
            case "idc15": // 鲜血猪灵
                return spawnBloodPiglin(location, config);
            case "idc16": // 暗影潜影贝
                return spawnShadowShulker(location, config);
            case "idc17": // 变异雪傀儡
                return spawnMutantSnowman(location, config);
            case "idc18": // 变异铁傀儡
                return spawnMutantIronGolem(location, config);
            case "idc19": // 变异僵尸Max
                return spawnMutantZombieMax(location, config);
            case "idc20": // 灵魂坚守者
                return spawnSoulGuardian(location, config);
            case "idc21": // 凋零领主
                return spawnWitherLord(location, config);
            case "idc22": // 异变之王
                return spawnMutationKing(location, config);
            // 其他实体将在后续添加
            default:
                logger.warning("暂不支持的实体ID: " + entityId);
                return null;
        }
    }

    /**
     * 生成变异僵尸01 (idc1)
     * 僵尸猪人形态，每次攻击对玩家施加剧毒效果
     */
    private LivingEntity spawnMutantZombie01(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.ZOMBIFIED_PIGLIN; // 默认为僵尸猪人
            }

            // 生成实体
            LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性
            String customName = config.customNameOverride != null ?
                config.customNameOverride : "§6变异僵尸01";
            entity.setCustomName(customName);
            entity.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制

            // 设置生命值
            double health = config.healthOverride > 0 ? config.healthOverride : 100.0;
            entity.setMaxHealth(health);
            entity.setHealth(health);

            // 设置装备
            setupEntityEquipment(entity, config);

            if (debugMode) {
                logger.info("成功生成变异僵尸01，生命值: " + health + "，名称: " + customName);
            }

            return entity;

        } catch (Exception e) {
            logger.severe("生成变异僵尸01失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 设置实体装备
     */
    private void setupEntityEquipment(LivingEntity entity, EntityOverrideConfig config) {
        if (entity.getEquipment() == null) {
            return;
        }

        // 设置武器
        if (config.weaponOverride != null && !config.weaponOverride.isEmpty()) {
            try {
                Material weaponMaterial = Material.valueOf(config.weaponOverride.toUpperCase());
                ItemStack weapon = new ItemStack(weaponMaterial);

                // 应用武器附魔
                for (Map.Entry<String, Integer> enchant : config.weaponEnchantments.entrySet()) {
                    try {
                        Enchantment enchantment = Enchantment.getByName(enchant.getKey().toUpperCase());
                        if (enchantment != null) {
                            weapon.addEnchantment(enchantment, enchant.getValue());
                        }
                    } catch (Exception e) {
                        logger.warning("应用武器附魔失败: " + enchant.getKey());
                    }
                }

                entity.getEquipment().setItemInMainHand(weapon);
                entity.getEquipment().setItemInMainHandDropChance(0.0F);

                if (debugMode) {
                    logger.info("为实体设置武器: " + weaponMaterial.name());
                }
            } catch (IllegalArgumentException e) {
                logger.warning("无效的武器材料: " + config.weaponOverride);
                // 设置默认武器（对于idc1是锋利1的铁剑）
                setDefaultWeaponForIdc1(entity);
            }
        } else {
            // 设置默认武器
            setDefaultWeaponForIdc1(entity);
        }

        // 设置护甲
        setupArmor(entity, config);
    }

    /**
     * 为idc1设置默认武器
     */
    private void setDefaultWeaponForIdc1(LivingEntity entity) {
        ItemStack defaultSword = new ItemStack(Material.IRON_SWORD);
        defaultSword.addEnchantment(Enchantment.SHARPNESS, 1); // 锋利1
        entity.getEquipment().setItemInMainHand(defaultSword);
        entity.getEquipment().setItemInMainHandDropChance(0.0F);

        if (debugMode) {
            logger.info("为idc1设置默认武器: 锋利1铁剑");
        }
    }

    /**
     * 设置护甲
     */
    private void setupArmor(LivingEntity entity, EntityOverrideConfig config) {
        // 使用新的armor_overrides配置
        if (config.armorOverrides != null && !config.armorOverrides.isEmpty()) {
            // 设置胸甲（IDC12的主要配置）
            if (config.armorOverrides.containsKey("chestplate")) {
                String chestplateMaterial = (String) config.armorOverrides.get("chestplate");
                String chestplateColor = (String) config.armorOverrides.get("chestplate_color");
                Map<String, Integer> chestplateEnchants = convertToEnchantmentMap(config.armorOverrides.get("chestplate_enchantments"));

                setAdvancedArmorPiece(entity, chestplateMaterial, chestplateColor, chestplateEnchants, "chestplate");
            }

            // 设置其他护甲部件
            if (config.armorOverrides.containsKey("helmet")) {
                String helmetMaterial = (String) config.armorOverrides.get("helmet");
                String helmetColor = (String) config.armorOverrides.get("helmet_color");
                Map<String, Integer> helmetEnchants = convertToEnchantmentMap(config.armorOverrides.get("helmet_enchantments"));

                setAdvancedArmorPiece(entity, helmetMaterial, helmetColor, helmetEnchants, "helmet");
            }

            if (config.armorOverrides.containsKey("leggings")) {
                String leggingsMaterial = (String) config.armorOverrides.get("leggings");
                String leggingsColor = (String) config.armorOverrides.get("leggings_color");
                Map<String, Integer> leggingsEnchants = convertToEnchantmentMap(config.armorOverrides.get("leggings_enchantments"));

                setAdvancedArmorPiece(entity, leggingsMaterial, leggingsColor, leggingsEnchants, "leggings");
            }

            if (config.armorOverrides.containsKey("boots")) {
                String bootsMaterial = (String) config.armorOverrides.get("boots");
                String bootsColor = (String) config.armorOverrides.get("boots_color");
                Map<String, Integer> bootsEnchants = convertToEnchantmentMap(config.armorOverrides.get("boots_enchantments"));

                setAdvancedArmorPiece(entity, bootsMaterial, bootsColor, bootsEnchants, "boots");
            }
        } else {
            // 使用旧的配置方式作为后备
            // 设置头盔
            if (config.helmetOverride != null && !config.helmetOverride.isEmpty()) {
                setArmorPiece(entity, config.helmetOverride, config.helmetEnchantments, "helmet");
            } else {
                // idc1默认穿全套皮革套
                setDefaultArmorForIdc1(entity);
            }

            // 设置胸甲
            if (config.chestplateOverride != null && !config.chestplateOverride.isEmpty()) {
                setArmorPiece(entity, config.chestplateOverride, config.chestplateEnchantments, "chestplate");
            }

            // 设置护腿
            if (config.leggingsOverride != null && !config.leggingsOverride.isEmpty()) {
                setArmorPiece(entity, config.leggingsOverride, config.leggingsEnchantments, "leggings");
            }

            // 设置靴子
            if (config.bootsOverride != null && !config.bootsOverride.isEmpty()) {
                setArmorPiece(entity, config.bootsOverride, config.bootsEnchantments, "boots");
            }
        }
    }

    /**
     * 为idc1设置默认护甲（全套皮革套）
     */
    private void setDefaultArmorForIdc1(LivingEntity entity) {
        entity.getEquipment().setHelmet(new ItemStack(Material.LEATHER_HELMET));
        entity.getEquipment().setChestplate(new ItemStack(Material.LEATHER_CHESTPLATE));
        entity.getEquipment().setLeggings(new ItemStack(Material.LEATHER_LEGGINGS));
        entity.getEquipment().setBoots(new ItemStack(Material.LEATHER_BOOTS));

        // 设置掉落概率为0
        entity.getEquipment().setHelmetDropChance(0.0F);
        entity.getEquipment().setChestplateDropChance(0.0F);
        entity.getEquipment().setLeggingsDropChance(0.0F);
        entity.getEquipment().setBootsDropChance(0.0F);

        if (debugMode) {
            logger.info("为idc1设置默认护甲: 全套皮革套");
        }
    }

    /**
     * 设置护甲部件
     */
    private void setArmorPiece(LivingEntity entity, String materialName, Map<String, Integer> enchantments, String type) {
        try {
            Material material = Material.valueOf(materialName.toUpperCase());
            ItemStack armor = new ItemStack(material);

            // 应用附魔
            for (Map.Entry<String, Integer> enchant : enchantments.entrySet()) {
                try {
                    Enchantment enchantment = Enchantment.getByName(enchant.getKey().toUpperCase());
                    if (enchantment != null) {
                        armor.addEnchantment(enchantment, enchant.getValue());
                    }
                } catch (Exception e) {
                    logger.warning("应用护甲附魔失败: " + enchant.getKey());
                }
            }

            // 根据类型设置护甲
            switch (type.toLowerCase()) {
                case "helmet":
                    entity.getEquipment().setHelmet(armor);
                    entity.getEquipment().setHelmetDropChance(0.0F);
                    break;
                case "chestplate":
                    entity.getEquipment().setChestplate(armor);
                    entity.getEquipment().setChestplateDropChance(0.0F);
                    break;
                case "leggings":
                    entity.getEquipment().setLeggings(armor);
                    entity.getEquipment().setLeggingsDropChance(0.0F);
                    break;
                case "boots":
                    entity.getEquipment().setBoots(armor);
                    entity.getEquipment().setBootsDropChance(0.0F);
                    break;
            }

            if (debugMode) {
                logger.info("为实体设置" + type + ": " + material.name());
            }

        } catch (IllegalArgumentException e) {
            logger.warning("无效的护甲材料: " + materialName);
        }
    }

    /**
     * 设置高级护甲部件（支持皮革护甲颜色）
     */
    private void setAdvancedArmorPiece(LivingEntity entity, String materialName, String color, Map<String, Integer> enchantments, String type) {
        if (materialName == null || materialName.isEmpty()) {
            return;
        }

        try {
            Material material = Material.valueOf(materialName.toUpperCase());
            ItemStack armor = new ItemStack(material);
            ItemMeta meta = armor.getItemMeta();

            // 如果是皮革护甲且指定了颜色，设置颜色
            if (meta instanceof LeatherArmorMeta && color != null && !color.isEmpty()) {
                LeatherArmorMeta leatherMeta = (LeatherArmorMeta) meta;
                try {
                    Color armorColor = getColorByName(color.toUpperCase());
                    if (armorColor != null) {
                        leatherMeta.setColor(armorColor);
                        if (debugMode) {
                            logger.info("为" + type + "设置颜色: " + color);
                        }
                    } else {
                        logger.warning("无效的护甲颜色: " + color + "，使用默认颜色");
                    }
                } catch (Exception e) {
                    logger.warning("设置护甲颜色时出错: " + color + "，使用默认颜色");
                }
            }

            // 应用附魔
            if (enchantments != null) {
                for (Map.Entry<String, Integer> enchant : enchantments.entrySet()) {
                    try {
                        Enchantment enchantment = Enchantment.getByName(enchant.getKey().toUpperCase());
                        if (enchantment != null) {
                            meta.addEnchant(enchantment, enchant.getValue(), true);
                        }
                    } catch (Exception e) {
                        logger.warning("应用护甲附魔失败: " + enchant.getKey());
                    }
                }
            }

            armor.setItemMeta(meta);

            // 根据类型设置护甲
            switch (type.toLowerCase()) {
                case "helmet":
                    entity.getEquipment().setHelmet(armor);
                    entity.getEquipment().setHelmetDropChance(0.0F);
                    break;
                case "chestplate":
                    entity.getEquipment().setChestplate(armor);
                    entity.getEquipment().setChestplateDropChance(0.0F);
                    break;
                case "leggings":
                    entity.getEquipment().setLeggings(armor);
                    entity.getEquipment().setLeggingsDropChance(0.0F);
                    break;
                case "boots":
                    entity.getEquipment().setBoots(armor);
                    entity.getEquipment().setBootsDropChance(0.0F);
                    break;
            }

            if (debugMode) {
                logger.info("为实体设置高级" + type + ": " + material.name() + (color != null ? " (颜色: " + color + ")" : ""));
            }

        } catch (IllegalArgumentException e) {
            logger.warning("无效的护甲材料: " + materialName);
        }
    }

    /**
     * 应用覆盖配置到实体
     */
    private void applyOverridesToEntity(LivingEntity entity, EntityOverrideConfig config) {
        // 设置生命值（如果在生成时没有设置）
        if (config.healthOverride > 0 && entity.getMaxHealth() != config.healthOverride) {
            entity.setMaxHealth(config.healthOverride);
            entity.setHealth(config.healthOverride);
        }

        // 应用药水效果
        for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effect : config.potionEffects.entrySet()) {
            try {
                PotionEffectType effectType = PotionEffectType.getByName(effect.getKey().toUpperCase());
                if (effectType != null) {
                    int duration = effect.getValue().duration == -1 ? Integer.MAX_VALUE : effect.getValue().duration;
                    entity.addPotionEffect(new PotionEffect(effectType, duration, effect.getValue().level));

                    if (debugMode) {
                        logger.info("为实体添加药水效果: " + effect.getKey() + " 等级" + effect.getValue().level);
                    }
                }
            } catch (Exception e) {
                logger.warning("应用药水效果失败: " + effect.getKey());
            }
        }

        // 应用速度倍数
        if (config.speedMultiplier > 0) {
            // 通过速度药水效果实现速度倍数
            int speedLevel = (int) Math.round(config.speedMultiplier - 1.0);
            if (speedLevel > 0) {
                entity.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, speedLevel - 1));

                if (debugMode) {
                    logger.info("为实体设置速度倍数: " + config.speedMultiplier);
                }
            }
        }
    }

    /**
     * 启用实体技能
     */
    private void enableEntitySkills(LivingEntity entity, String entityId, EntityOverrideConfig config) {
        try {
            switch (entityId) {
                case "idc1": // 变异僵尸01
                    enableMutantZombie01Skills(entity, config);
                    break;
                case "idc5": // 变异末影螨
                    if (entity instanceof org.bukkit.entity.Endermite) {
                        enableMutantEndermiteSkills((org.bukkit.entity.Endermite) entity, config);
                    }
                    break;
                case "idc6": // 变异蜘蛛
                    if (entity instanceof org.bukkit.entity.CaveSpider) {
                        enableMutantSpiderSkills((org.bukkit.entity.CaveSpider) entity, config);
                    }
                    break;
                case "idc7": // 灾厄卫道士
                    if (entity instanceof org.bukkit.entity.Pillager) {
                        enableDisasterGuardianSkills((org.bukkit.entity.Pillager) entity, config);
                    }
                    break;
                case "idc8": // 灾厄唤魔者
                    if (entity instanceof org.bukkit.entity.Evoker) {
                        enableDisasterSummonerSkills((org.bukkit.entity.Evoker) entity, config);
                    }
                    break;
                case "idc9": // 灾厄劫掠兽
                    if (entity instanceof org.bukkit.entity.Ravager) {
                        enableDisasterRavagerBeastSkills((org.bukkit.entity.Ravager) entity, config);
                    }
                    break;
                case "idc10": // 变异僵尸马
                    if (entity instanceof org.bukkit.entity.ZombieHorse) {
                        enableMutantZombieHorseSkills((org.bukkit.entity.ZombieHorse) entity, config);
                    }
                    break;
                case "idc11": // 变异岩浆怪
                    if (entity instanceof org.bukkit.entity.MagmaCube) {
                        enableMutantMagmaCubeSkills((org.bukkit.entity.MagmaCube) entity, config);
                    }
                    break;
                case "idc12": // 变异尸壳
                    if (entity instanceof org.bukkit.entity.Husk) {
                        enableMutantHuskSkills((org.bukkit.entity.Husk) entity, config);
                    }
                    break;
                case "idc13": // 变异僵尸3
                    if (entity instanceof org.bukkit.entity.Stray) {
                        enableSwampStraySkills((org.bukkit.entity.Stray) entity, config);
                    }
                    break;
                case "idc14": // 变异僵尸4
                    if (entity instanceof org.bukkit.entity.WitherSkeleton) {
                        enableMutantZombie04Skills((org.bukkit.entity.WitherSkeleton) entity, config);
                    }
                    break;
                case "idc15": // 鲜血猪灵
                    if (entity instanceof org.bukkit.entity.Piglin) {
                        enableBloodPiglinSkills((org.bukkit.entity.Piglin) entity, config);
                    }
                    break;
                case "idc16": // 暗影潜影贝
                    if (entity instanceof org.bukkit.entity.Shulker) {
                        enableShadowShulkerSkills((org.bukkit.entity.Shulker) entity, config);
                    }
                    break;
                case "idc17": // 变异雪傀儡
                    if (entity instanceof org.bukkit.entity.Snowman) {
                        enableMutantSnowmanSkills((org.bukkit.entity.Snowman) entity, config);
                    }
                    break;
                case "idc18": // 变异铁傀儡
                    if (entity instanceof org.bukkit.entity.IronGolem) {
                        enableMutantIronGolemSkills((org.bukkit.entity.IronGolem) entity, config);
                    }
                    break;
                case "idc19": // 变异僵尸Max
                    if (entity instanceof org.bukkit.entity.Drowned) {
                        enableMutantZombieMaxSkills((org.bukkit.entity.Drowned) entity, config);
                    }
                    break;
                case "idc20": // 灵魂坚守者
                    enableSoulGuardianSkills(entity, config);
                    break;
                case "idc21": // 凋零领主
                    if (entity instanceof org.bukkit.entity.Wither) {
                        enableWitherLordSkills((org.bukkit.entity.Wither) entity, config);
                    }
                    break;
                case "idc22": // 异变之王
                    if (entity instanceof org.bukkit.entity.EnderDragon) {
                        enableMutationKingSkills((org.bukkit.entity.EnderDragon) entity, config);
                    }
                    break;
                // 其他实体技能将在后续添加
                default:
                    if (debugMode) {
                        logger.info("实体 " + entityId + " 暂无特殊技能");
                    }
                    break;
            }
        } catch (Exception e) {
            logger.warning("启用实体技能失败: " + e.getMessage());
        }
    }

    /**
     * 启用变异僵尸01的技能（剧毒攻击 + 粒子效果）
     */
    private void enableMutantZombie01Skills(LivingEntity entity, EntityOverrideConfig config) {
        // 添加剧毒攻击标记
        entity.setMetadata("poisonAttacker", new FixedMetadataValue(plugin, true));

        // 从配置中获取剧毒参数
        int poisonLevel = 0;
        int poisonDuration = 60; // 3秒，默认值
        double poisonChance = 0.8; // 默认80%概率

        if (config.specialAbilities.containsKey("poison_level")) {
            poisonLevel = (Integer) config.specialAbilities.get("poison_level");
        }

        if (config.specialAbilities.containsKey("poison_duration")) {
            poisonDuration = (Integer) config.specialAbilities.get("poison_duration");
        }

        if (config.specialAbilities.containsKey("poison_chance")) {
            Object chanceObj = config.specialAbilities.get("poison_chance");
            if (chanceObj instanceof Double) {
                poisonChance = (Double) chanceObj;
            } else if (chanceObj instanceof Integer) {
                poisonChance = ((Integer) chanceObj).doubleValue();
            }
        }

        // 设置剧毒参数
        entity.setMetadata("poisonLevel", new FixedMetadataValue(plugin, poisonLevel));
        entity.setMetadata("poisonDuration", new FixedMetadataValue(plugin, poisonDuration));
        entity.setMetadata("poisonChance", new FixedMetadataValue(plugin, poisonChance));

        // 启用粒子效果技能
        boolean particleEnabled = true;
        if (config.specialAbilities.containsKey("particle_enabled")) {
            particleEnabled = (Boolean) config.specialAbilities.get("particle_enabled");
        }

        if (particleEnabled) {
            startMutantZombie01ParticleEffect(entity, config);
        }

        if (debugMode) {
            logger.info("为变异僵尸01启用剧毒攻击技能，等级: " + poisonLevel + "，持续时间: " + poisonDuration + "tick");
            logger.info("为变异僵尸01启用粒子效果技能: " + particleEnabled);
        }
    }

    /**
     * 使用默认设置生成实体（调用原有系统）
     */
    private LivingEntity spawnWithDefaultSettings(Location location, String entityId) {
        // 这里需要调用原有的AdvancedEntitySpawner或ZombieHelper
        // 暂时返回null，等待集成
        return null;
    }

    /**
     * 创建默认配置
     */
    private EntityOverrideConfig createDefaultConfig(String entityId) {
        EntityOverrideConfig config = new EntityOverrideConfig();
        config.enabled = true;

        // 根据entityId设置默认值
        switch (entityId) {
            case "idc1":
                config.healthOverride = 100.0;
                config.customNameOverride = "§6变异僵尸01";
                config.entityTypeOverride = "ZOMBIFIED_PIGLIN";
                config.weaponOverride = "IRON_SWORD";
                config.weaponEnchantments.put("SHARPNESS", 1);
                break;
            // 其他实体的默认配置将在后续添加
        }

        return config;
    }

    // Getter方法
    public boolean isUserCustomEnabled() {
        return useUserCustomSettings;
    }

    public boolean isDefaultEnabled() {
        return useDefaultSettings;
    }

    public String getPriorityStrategy() {
        return priorityStrategy;
    }

    public boolean isDebugMode() {
        return debugMode;
    }

    public AdvancedEntitySkillHandler getAdvancedSkillHandler() {
        return advancedSkillHandler;
    }

    public Map<String, EntityOverrideConfig> getConfigCache() {
        return new HashMap<>(configCache);
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        loadConfig();
        logger.info("UserCustomEntity配置已重载");
    }

    /**
     * 生成变异僵尸02 (idc2)
     * 骷髅形态，冲击2弓，螺旋紫色粒子效果
     */
    private LivingEntity spawnMutantZombie02(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.SKELETON; // 默认为骷髅
            }

            // 生成实体
            LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                entity.setMaxHealth(config.healthOverride);
                entity.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                entity.setCustomName(config.customNameOverride);
                entity.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置装备
            if (config.weaponOverride != null) {
                ItemStack weapon = new ItemStack(Material.valueOf(config.weaponOverride));

                // 添加武器附魔
                if (config.weaponEnchantments != null && !config.weaponEnchantments.isEmpty()) {
                    ItemMeta weaponMeta = weapon.getItemMeta();
                    for (Map.Entry<String, Integer> enchant : config.weaponEnchantments.entrySet()) {
                        try {
                            Enchantment enchantment = Enchantment.getByKey(org.bukkit.NamespacedKey.minecraft(enchant.getKey().toLowerCase()));
                            if (enchantment != null) {
                                weaponMeta.addEnchant(enchantment, enchant.getValue(), true);
                            }
                        } catch (Exception e) {
                            logger.warning("无法添加附魔 " + enchant.getKey() + ": " + e.getMessage());
                        }
                    }
                    weapon.setItemMeta(weaponMeta);
                }

                entity.getEquipment().setItemInMainHand(weapon);
            }

            // 设置头盔
            if (config.helmetOverride != null) {
                try {
                    ItemStack helmet = new ItemStack(Material.valueOf(config.helmetOverride));
                    entity.getEquipment().setHelmet(helmet);
                } catch (Exception e) {
                    logger.warning("无法设置头盔 " + config.helmetOverride + ": " + e.getMessage());
                }
            }

            // 设置药水效果
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int level = effectConfig.level;
                            int duration = effectConfig.duration;

                            if (duration == -1) {
                                duration = Integer.MAX_VALUE; // 永久效果
                            }

                            PotionEffect effect = new PotionEffect(effectType, duration, level);
                            entity.addPotionEffect(effect);

                            if (debugMode) {
                                logger.info("为idc2添加药水效果: " + effectName + " 等级" + (level + 1) + " 持续" + (duration == Integer.MAX_VALUE ? "永久" : duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("设置药水效果时出错: " + e.getMessage());
                    }
                }
            }

            // 启用变异僵尸02的技能
            enableMutantZombie02Skills(entity, config);

            // 设置元数据标记
            entity.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            entity.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            entity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            entity.setMetadata("entityId", new FixedMetadataValue(plugin, "idc2"));

            if (debugMode) {
                logger.info("成功生成变异僵尸02，生命值: " + entity.getHealth() + "/" + entity.getMaxHealth() +
                           "，名称: " + entity.getCustomName());
            }

            return entity;

        } catch (Exception e) {
            logger.severe("生成变异僵尸02时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异僵尸02的技能（螺旋紫色粒子效果）
     */
    private void enableMutantZombie02Skills(LivingEntity entity, EntityOverrideConfig config) {
        // 启用粒子效果技能
        boolean particleEnabled = true;
        if (config.specialAbilities.containsKey("particle_enabled")) {
            particleEnabled = (Boolean) config.specialAbilities.get("particle_enabled");
        }

        if (particleEnabled) {
            startMutantZombie02ParticleEffect(entity, config);
        }

        if (debugMode) {
            logger.info("为变异僵尸02启用螺旋粒子效果技能: " + particleEnabled);
        }
    }

    /**
     * 生成变异烈焰人 (idc3)
     * 烈焰人形态，无装备，速度4，烈焰粒子和烈焰弹攻击
     */
    private LivingEntity spawnMutantBlaze(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.BLAZE; // 默认为烈焰人
            }

            // 生成实体
            Blaze blaze = (Blaze) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                blaze.setMaxHealth(config.healthOverride);
                blaze.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                blaze.setCustomName(config.customNameOverride);
                blaze.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度4）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int level = effectConfig.level;
                            int duration = effectConfig.duration;

                            if (duration == -1) {
                                duration = Integer.MAX_VALUE; // 永久效果
                            }

                            PotionEffect effect = new PotionEffect(effectType, duration, level);
                            blaze.addPotionEffect(effect);

                            if (debugMode) {
                                logger.info("为idc3添加药水效果: " + effectName + " 等级" + (level + 1) + " 持续" + (duration == Integer.MAX_VALUE ? "永久" : duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("设置药水效果时出错: " + e.getMessage());
                    }
                }
            }

            // 启用变异烈焰人的技能
            enableMutantBlazeSkills(blaze, config);

            // 设置元数据标记
            blaze.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            blaze.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            blaze.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            blaze.setMetadata("entityId", new FixedMetadataValue(plugin, "idc3"));

            if (debugMode) {
                logger.info("成功生成变异烈焰人，生命值: " + blaze.getHealth() + "/" + blaze.getMaxHealth() +
                           "，名称: " + blaze.getCustomName());
            }

            return blaze;

        } catch (Exception e) {
            logger.severe("生成变异烈焰人时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异烈焰人的技能（烈焰粒子和烈焰弹攻击）
     */
    private void enableMutantBlazeSkills(Blaze blaze, EntityOverrideConfig config) {
        // 启用攻击技能
        boolean attackEnabled = true;
        if (config.specialAbilities.containsKey("attack_enabled")) {
            attackEnabled = (Boolean) config.specialAbilities.get("attack_enabled");
        }

        if (attackEnabled) {
            startMutantBlazeAttacks(blaze, config);
        }

        if (debugMode) {
            logger.info("为变异烈焰人启用攻击技能: " + attackEnabled);
        }
    }

    /**
     * 启动变异烈焰人的攻击技能（烈焰粒子和烈焰弹）
     */
    private void startMutantBlazeAttacks(Blaze blaze, EntityOverrideConfig config) {
        // 从配置中获取攻击参数
        int attackInterval = 60; // 默认每3秒攻击一次
        double attackRange = 15.0; // 默认攻击范围15格
        int particleDamage = 4; // 烈焰粒子伤害
        int fireballDamage = 8; // 烈焰弹伤害（通过爆炸实现）

        if (config.specialAbilities.containsKey("attack_interval")) {
            attackInterval = (Integer) config.specialAbilities.get("attack_interval");
        }
        if (config.specialAbilities.containsKey("attack_range")) {
            Object rangeObj = config.specialAbilities.get("attack_range");
            if (rangeObj instanceof Double) {
                attackRange = (Double) rangeObj;
            } else if (rangeObj instanceof Integer) {
                attackRange = ((Integer) rangeObj).doubleValue();
            }
        }
        if (config.specialAbilities.containsKey("particle_damage")) {
            particleDamage = (Integer) config.specialAbilities.get("particle_damage");
        }
        if (config.specialAbilities.containsKey("fireball_damage")) {
            fireballDamage = (Integer) config.specialAbilities.get("fireball_damage");
        }

        final int finalParticleDamage = particleDamage;
        final double finalAttackRange = attackRange;
        final int finalFireballDamage = fireballDamage;

        BukkitTask attackTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (blaze == null || blaze.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(blaze);
                    return;
                }

                // 查找范围内最近的玩家
                Player target = null;
                double closestDistance = finalAttackRange + 1;

                for (Entity entity : blaze.getNearbyEntities(finalAttackRange, finalAttackRange, finalAttackRange)) {
                    if (entity instanceof Player) {
                        double distance = blaze.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    final Player finalTarget = target;

                    // 让烈焰人面向目标玩家
                    blaze.teleport(blaze.getLocation().setDirection(
                            finalTarget.getLocation().subtract(blaze.getLocation()).toVector()));

                    // 发射烈焰粒子（5条粒子流）
                    for (int i = 0; i < 5; i++) {
                        new BukkitRunnable() {
                            double distance = 0;
                            Location particleLoc;

                            @Override
                            public void run() {
                                if (blaze == null || blaze.isDead()) {
                                    cancel();
                                    return;
                                }

                                if (distance == 0) {
                                    // 初始化粒子位置
                                    particleLoc = blaze.getLocation().add(0, 1, 0).clone();
                                }

                                // 移动粒子
                                particleLoc.add(blaze.getLocation().getDirection().multiply(0.7));
                                distance += 0.7;

                                // 显示火焰粒子
                                blaze.getWorld().spawnParticle(org.bukkit.Particle.FLAME, particleLoc, 5, 0.1, 0.1, 0.1, 0.02);

                                // 检测是否击中玩家
                                for (Entity entity : blaze.getWorld().getNearbyEntities(particleLoc, 0.8, 0.8, 0.8)) {
                                    if (entity instanceof Player) {
                                        Player player = (Player) entity;
                                        player.damage(finalParticleDamage, blaze); // 造成配置的伤害
                                        player.setFireTicks(60); // 着火3秒

                                        // 显示命中效果
                                        blaze.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, particleLoc, 1, 0, 0, 0, 0);
                                        blaze.getWorld().playSound(particleLoc, org.bukkit.Sound.ENTITY_PLAYER_HURT_ON_FIRE, 1.0f, 1.0f);

                                        cancel();
                                        return;
                                    }
                                }

                                // 超过最大距离或碰到非透明方块时停止
                                if (distance > finalAttackRange || !particleLoc.getBlock().isPassable()) {
                                    // 显示命中效果
                                    blaze.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, particleLoc, 3, 0.2, 0.2, 0.2, 0.02);
                                    cancel();
                                }
                            }
                        }.runTaskTimer(plugin, i * 5, 1); // 每个粒子流间隔5刻启动，每刻更新一次
                    }

                    // 发射烈焰弹
                    SmallFireball fireball = blaze.launchProjectile(SmallFireball.class);
                    fireball.setVelocity(blaze.getLocation().getDirection().multiply(1.2));
                    fireball.setMetadata("mutantBlazeFireball", new FixedMetadataValue(plugin, true));
                    fireball.setMetadata("fireballDamage", new FixedMetadataValue(plugin, finalFireballDamage));

                    // 播放发射音效
                    blaze.getWorld().playSound(blaze.getLocation(), org.bukkit.Sound.ENTITY_BLAZE_SHOOT, 1.0f, 1.0f);
                }
            }
        }.runTaskTimer(plugin, 40, attackInterval); // 2秒后开始，按配置间隔执行

        // 保存任务以便后续取消
        particleEffectTasks.put(blaze, attackTask);

        if (debugMode) {
            logger.info("为变异烈焰人启动攻击任务，间隔: " + attackInterval + "tick，范围: " + attackRange + "格");
        }
    }

    /**
     * 生成变异爬行者 (idc4)
     * 闪电苦力怕形态，无装备，速度6，闪电攻击
     */
    private LivingEntity spawnMutantCreeper(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.CREEPER; // 默认为苦力怕
            }

            // 生成实体
            Creeper creeper = (Creeper) location.getWorld().spawnEntity(location, entityType);

            // 设置为带电爬行者（闪电苦力怕）
            creeper.setPowered(true);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                creeper.setMaxHealth(config.healthOverride);
                creeper.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                creeper.setCustomName(config.customNameOverride);
                creeper.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度6）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int level = effectConfig.level;
                            int duration = effectConfig.duration;

                            if (duration == -1) {
                                duration = Integer.MAX_VALUE; // 永久效果
                            }

                            PotionEffect effect = new PotionEffect(effectType, duration, level);
                            creeper.addPotionEffect(effect);

                            if (debugMode) {
                                logger.info("为idc4添加药水效果: " + effectName + " 等级" + (level + 1) + " 持续" + (duration == Integer.MAX_VALUE ? "永久" : duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("设置药水效果时出错: " + e.getMessage());
                    }
                }
            }

            // 设置爆炸相关属性
            if (config.specialAbilities.containsKey("explosion_radius")) {
                Object radiusObj = config.specialAbilities.get("explosion_radius");
                if (radiusObj instanceof Integer) {
                    creeper.setExplosionRadius((Integer) radiusObj);
                } else if (radiusObj instanceof Double) {
                    creeper.setExplosionRadius(((Double) radiusObj).intValue());
                }
            }

            // 防止破坏方块
            boolean preventBlockDamage = true;
            if (config.specialAbilities.containsKey("prevent_block_damage")) {
                preventBlockDamage = (Boolean) config.specialAbilities.get("prevent_block_damage");
            }
            if (preventBlockDamage) {
                creeper.setMetadata("doNotDestroy", new FixedMetadataValue(plugin, true));
            }

            // 启用变异爬行者的技能
            enableMutantCreeperSkills(creeper, config);

            // 设置元数据标记
            creeper.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            creeper.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            creeper.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            creeper.setMetadata("entityId", new FixedMetadataValue(plugin, "idc4"));

            if (debugMode) {
                logger.info("成功生成变异爬行者，生命值: " + creeper.getHealth() + "/" + creeper.getMaxHealth() +
                           "，名称: " + creeper.getCustomName());
            }

            return creeper;

        } catch (Exception e) {
            logger.severe("生成变异爬行者时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成变异末影螨 (idc5)
     * 末影螨形态，150血量，速度5，电流攻击
     */
    private LivingEntity spawnMutantEndermite(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.ENDERMITE; // 默认为末影螨
            }

            // 生成实体
            org.bukkit.entity.Endermite endermite = (org.bukkit.entity.Endermite) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                endermite.setMaxHealth(config.healthOverride);
                endermite.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                endermite.setCustomName(config.customNameOverride);
                endermite.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 启用变异末影螨的技能
            enableMutantEndermiteSkills(endermite, config);

            // 设置元数据标记
            endermite.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            endermite.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            endermite.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            endermite.setMetadata("entityId", new FixedMetadataValue(plugin, "idc5"));

            if (debugMode) {
                logger.info("成功生成变异末影螨，生命值: " + endermite.getHealth() + "/" + endermite.getMaxHealth() +
                           "，名称: " + endermite.getCustomName());
            }

            return endermite;

        } catch (Exception e) {
            logger.severe("生成变异末影螨时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成变异蜘蛛 (idc6)
     * 洞穴蜘蛛形态，300血量，速度4+力量3，五大技能系统
     */
    private LivingEntity spawnMutantSpider(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.CAVE_SPIDER; // 默认为洞穴蜘蛛
            }

            // 生成实体
            org.bukkit.entity.CaveSpider spider = (org.bukkit.entity.CaveSpider) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                spider.setMaxHealth(config.healthOverride);
                spider.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                spider.setCustomName(config.customNameOverride);
                spider.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 启用变异蜘蛛的技能
            enableMutantSpiderSkills(spider, config);

            // 设置元数据标记
            spider.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            spider.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            spider.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            spider.setMetadata("entityId", new FixedMetadataValue(plugin, "idc6"));

            if (debugMode) {
                logger.info("成功生成变异蜘蛛，生命值: " + spider.getHealth() + "/" + spider.getMaxHealth() +
                           "，名称: " + spider.getCustomName());
            }

            return spider;

        } catch (Exception e) {
            logger.severe("生成变异蜘蛛时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成灾厄卫道士 (idc7)
     * 掠夺者形态，540血量，抗性提升II，三大技能系统
     */
    private LivingEntity spawnDisasterGuardian(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.PILLAGER; // 默认为掠夺者
            }

            // 生成实体
            org.bukkit.entity.Pillager pillager = (org.bukkit.entity.Pillager) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                pillager.setMaxHealth(config.healthOverride);
                pillager.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                pillager.setCustomName(config.customNameOverride);
                pillager.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置装备 - 下界合金斧
            setupDisasterGuardianEquipment(pillager, config);

            // 启用灾厄卫道士的技能
            enableDisasterGuardianSkills(pillager, config);

            // 设置元数据标记
            pillager.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            pillager.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            pillager.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            pillager.setMetadata("entityId", new FixedMetadataValue(plugin, "idc7"));
            pillager.setMetadata("disasterGuardian", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成灾厄卫道士，生命值: " + pillager.getHealth() + "/" + pillager.getMaxHealth() +
                           "，名称: " + pillager.getCustomName());
            }

            return pillager;

        } catch (Exception e) {
            logger.severe("生成灾厄卫道士时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 设置灾厄卫道士的装备
     */
    private void setupDisasterGuardianEquipment(org.bukkit.entity.Pillager pillager, EntityOverrideConfig config) {
        try {
            // 给掠夺者装备附魔弩
            ItemStack crossbow = new ItemStack(Material.CROSSBOW);
            ItemMeta crossbowMeta = crossbow.getItemMeta();
            if (crossbowMeta != null) {
                crossbowMeta.setDisplayName("§4灾厄之弩");
                crossbowMeta.addEnchant(Enchantment.PIERCING, 4, true); // 穿透IV
                crossbowMeta.addEnchant(Enchantment.QUICK_CHARGE, 3, true); // 快速装填III
                crossbowMeta.addEnchant(Enchantment.MULTISHOT, 1, true); // 多重射击I
                crossbow.setItemMeta(crossbowMeta);
            }

            // 设置装备
            pillager.getEquipment().setItemInMainHand(crossbow);
            pillager.getEquipment().setItemInMainHandDropChance(0.0F); // 不掉落

            if (debugMode) {
                logger.info("为灾厄卫道士设置了附魔弩装备");
            }
        } catch (Exception e) {
            logger.warning("设置灾厄卫道士装备时出错: " + e.getMessage());
        }
    }

    /**
     * 生成灾厄唤魔者 (idc8)
     * 唤魔者形态，600血量，速度II，三大技能系统
     */
    private LivingEntity spawnDisasterSummoner(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.EVOKER; // 默认为唤魔者
            }

            // 生成实体
            org.bukkit.entity.Evoker evoker = (org.bukkit.entity.Evoker) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                evoker.setMaxHealth(config.healthOverride);
                evoker.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                evoker.setCustomName(config.customNameOverride);
                evoker.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 启用灾厄唤魔者的技能
            enableDisasterSummonerSkills(evoker, config);

            // 设置元数据标记
            evoker.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            evoker.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            evoker.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            evoker.setMetadata("entityId", new FixedMetadataValue(plugin, "idc8"));
            evoker.setMetadata("disasterSummoner", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成灾厄唤魔者，生命值: " + evoker.getHealth() + "/" + evoker.getMaxHealth() +
                           "，名称: " + evoker.getCustomName());
            }

            return evoker;

        } catch (Exception e) {
            logger.severe("生成灾厄唤魔者时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成灾厄劫掠兽 (idc9)
     * 劫掠兽形态，1000血量，跳跃提升III+速度I，脚底暴击圆圈技能
     */
    private LivingEntity spawnDisasterRavagerBeast(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.RAVAGER; // 默认为劫掠兽
            }

            // 生成实体
            org.bukkit.entity.Ravager ravager = (org.bukkit.entity.Ravager) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                ravager.setMaxHealth(config.healthOverride);
                ravager.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                ravager.setCustomName(config.customNameOverride);
                ravager.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 启用灾厄劫掠兽的技能
            enableDisasterRavagerBeastSkills(ravager, config);

            // 设置元数据标记
            ravager.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            ravager.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            ravager.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            ravager.setMetadata("entityId", new FixedMetadataValue(plugin, "idc9"));
            ravager.setMetadata("disasterRavagerBeast", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成灾厄劫掠兽，生命值: " + ravager.getHealth() + "/" + ravager.getMaxHealth() +
                           "，名称: " + ravager.getCustomName());
            }

            return ravager;

        } catch (Exception e) {
            logger.severe("生成灾厄劫掠兽时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 生成变异僵尸马 (idc10)
     * 僵尸马形态，300血量，速度V，五大技能系统
     */
    private LivingEntity spawnMutantZombieHorse(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.ZOMBIE_HORSE; // 默认为僵尸马
            }

            // 生成实体
            org.bukkit.entity.ZombieHorse zombieHorse = (org.bukkit.entity.ZombieHorse) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                zombieHorse.setMaxHealth(config.healthOverride);
                zombieHorse.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                zombieHorse.setCustomName(config.customNameOverride);
                zombieHorse.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置特殊属性
            setupMutantZombieHorseAttributes(zombieHorse, config);

            // 启用变异僵尸马的技能
            enableMutantZombieHorseSkills(zombieHorse, config);

            // 设置元数据标记
            zombieHorse.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            zombieHorse.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            zombieHorse.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            zombieHorse.setMetadata("entityId", new FixedMetadataValue(plugin, "idc10"));
            zombieHorse.setMetadata("mutantZombieHorse", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成变异僵尸马，生命值: " + zombieHorse.getHealth() + "/" + zombieHorse.getMaxHealth() +
                           "，名称: " + zombieHorse.getCustomName());
            }

            return zombieHorse;

        } catch (Exception e) {
            logger.severe("生成变异僵尸马时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 设置变异僵尸马的特殊属性
     */
    private void setupMutantZombieHorseAttributes(org.bukkit.entity.ZombieHorse zombieHorse, EntityOverrideConfig config) {
        try {
            // 启用AI - 需要AI来支持移动
            zombieHorse.setAI(true);

            // 设置为不会被驯服
            zombieHorse.setTamed(false);

            // 防止实体消失
            zombieHorse.setRemoveWhenFarAway(false);

            // 启用重力 - 需要重力来正常移动
            zombieHorse.setGravity(true);

            // 应用NPCAI逻辑 - 让马能够移动和追踪
            applyNPCAIToZombieHorse(zombieHorse);

            if (debugMode) {
                logger.info("为变异僵尸马设置了特殊属性：启用AI、不可驯服、启用重力、应用NPCAI");
            }
        } catch (Exception e) {
            logger.warning("设置变异僵尸马特殊属性时出错: " + e.getMessage());
        }
    }

    /**
     * 为僵尸马应用NPCAI逻辑
     */
    private void applyNPCAIToZombieHorse(org.bukkit.entity.ZombieHorse zombieHorse) {
        try {
            // 设置移动速度（使用兼容的方式，参考UserCustomZombie的实现）
            try {
                org.bukkit.attribute.AttributeInstance speedAttr = null;

                // 使用现代化的属性获取方式（避免弃用API）
                try {
                    // 尝试使用反射获取属性常量，避免弃用的values()和name()方法
                    Class<?> attributeClass = org.bukkit.attribute.Attribute.class;

                    // 尝试常见的移动速度属性名称
                    String[] possibleNames = {
                        "GENERIC_MOVEMENT_SPEED",
                        "MOVEMENT_SPEED",
                        "HORSE_MOVEMENT_SPEED"
                    };

                    for (String attrName : possibleNames) {
                        try {
                            java.lang.reflect.Field field = attributeClass.getField(attrName);
                            org.bukkit.attribute.Attribute attr = (org.bukkit.attribute.Attribute) field.get(null);
                            speedAttr = zombieHorse.getAttribute(attr);
                            if (speedAttr != null) {
                                break;
                            }
                        } catch (Exception ignored) {
                            // 继续尝试下一个属性名称
                        }
                    }
                } catch (Exception e) {
                    if (debugMode) {
                        logger.info("无法找到移动速度属性，跳过设置");
                    }
                }

                if (speedAttr != null) {
                    speedAttr.setBaseValue(0.3);
                    if (debugMode) {
                        logger.info("成功设置移动速度属性: 0.3");
                    }
                }
            } catch (Exception e) {
                if (debugMode) {
                    logger.info("设置移动速度失败: " + e.getMessage());
                }
            }

            // 设置跟随范围（使用兼容的方式）
            try {
                org.bukkit.attribute.AttributeInstance followAttr = null;

                // 使用现代化的属性获取方式（避免弃用API）
                try {
                    // 尝试使用反射获取属性常量，避免弃用的values()和name()方法
                    Class<?> attributeClass = org.bukkit.attribute.Attribute.class;

                    // 尝试常见的跟随范围属性名称
                    String[] possibleNames = {
                        "GENERIC_FOLLOW_RANGE",
                        "FOLLOW_RANGE"
                    };

                    for (String attrName : possibleNames) {
                        try {
                            java.lang.reflect.Field field = attributeClass.getField(attrName);
                            org.bukkit.attribute.Attribute attr = (org.bukkit.attribute.Attribute) field.get(null);
                            followAttr = zombieHorse.getAttribute(attr);
                            if (followAttr != null) {
                                break;
                            }
                        } catch (Exception ignored) {
                            // 继续尝试下一个属性名称
                        }
                    }
                } catch (Exception e) {
                    if (debugMode) {
                        logger.info("无法找到跟随范围属性，跳过设置");
                    }
                }

                if (followAttr != null) {
                    followAttr.setBaseValue(30.0);
                    if (debugMode) {
                        logger.info("成功设置跟随范围属性: 30.0");
                    }
                }
            } catch (Exception e) {
                if (debugMode) {
                    logger.info("设置跟随范围失败: " + e.getMessage());
                }
            }

            if (debugMode) {
                logger.info("为变异僵尸马应用了NPCAI逻辑");
            }
        } catch (Exception e) {
            logger.warning("应用僵尸马NPCAI逻辑时出错: " + e.getMessage());
        }
    }

    /**
     * 生成变异岩浆怪 (idc11)
     * 岩浆怪形态，100血量，速度III，大尺寸，火焰粒子环技能
     */
    private LivingEntity spawnMutantMagmaCube(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.MAGMA_CUBE; // 默认为岩浆怪
            }

            // 生成实体
            org.bukkit.entity.MagmaCube magmaCube = (org.bukkit.entity.MagmaCube) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                magmaCube.setMaxHealth(config.healthOverride);
                magmaCube.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                magmaCube.setCustomName(config.customNameOverride);
                magmaCube.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置特殊属性
            setupMutantMagmaCubeAttributes(magmaCube, config);

            // 启用变异岩浆怪的技能
            enableMutantMagmaCubeSkills(magmaCube, config);

            // 设置元数据标记
            magmaCube.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            magmaCube.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            magmaCube.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            magmaCube.setMetadata("entityId", new FixedMetadataValue(plugin, "idc11"));
            magmaCube.setMetadata("mutantMagmaCube", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成变异岩浆怪，生命值: " + magmaCube.getHealth() + "/" + magmaCube.getMaxHealth() +
                           "，名称: " + magmaCube.getCustomName() + "，尺寸: " + magmaCube.getSize());
            }

            return magmaCube;

        } catch (Exception e) {
            logger.severe("生成变异岩浆怪时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 设置变异岩浆怪的特殊属性
     */
    private void setupMutantMagmaCubeAttributes(org.bukkit.entity.MagmaCube magmaCube, EntityOverrideConfig config) {
        try {
            // 设置岩浆怪尺寸（从配置获取，默认为3）
            int size = 3;
            if (config.specialAbilities.containsKey("size")) {
                size = (Integer) config.specialAbilities.get("size");
            }
            magmaCube.setSize(size);

            // 防止实体消失
            magmaCube.setRemoveWhenFarAway(false);

            if (debugMode) {
                logger.info("为变异岩浆怪设置了特殊属性：尺寸=" + size + "，不会消失");
            }
        } catch (Exception e) {
            logger.warning("设置变异岩浆怪特殊属性时出错: " + e.getMessage());
        }
    }

    /**
     * 启用变异爬行者的技能（闪电攻击）
     */
    private void enableMutantCreeperSkills(Creeper creeper, EntityOverrideConfig config) {
        // 启用闪电攻击技能
        boolean lightningEnabled = true;
        if (config.specialAbilities.containsKey("lightning_enabled")) {
            lightningEnabled = (Boolean) config.specialAbilities.get("lightning_enabled");
        }

        if (lightningEnabled) {
            startMutantCreeperLightningAttack(creeper, config);
        }

        if (debugMode) {
            logger.info("为变异爬行者启用闪电攻击技能: " + lightningEnabled);
        }
    }

    /**
     * 启动变异爬行者的闪电攻击技能
     */
    private void startMutantCreeperLightningAttack(Creeper creeper, EntityOverrideConfig config) {
        // 从配置中获取攻击参数
        int attackInterval = 100; // 默认每5秒攻击一次
        double attackRange = 20.0; // 默认攻击范围20格

        if (config.specialAbilities.containsKey("lightning_interval")) {
            attackInterval = (Integer) config.specialAbilities.get("lightning_interval");
        }
        if (config.specialAbilities.containsKey("lightning_range")) {
            Object rangeObj = config.specialAbilities.get("lightning_range");
            if (rangeObj instanceof Double) {
                attackRange = (Double) rangeObj;
            } else if (rangeObj instanceof Integer) {
                attackRange = ((Integer) rangeObj).doubleValue();
            }
        }

        final double finalAttackRange = attackRange;

        BukkitTask lightningTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (creeper == null || creeper.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(creeper);
                    return;
                }

                // 查找范围内最近的玩家
                Player target = null;
                double closestDistance = finalAttackRange + 1;

                for (Entity entity : creeper.getNearbyEntities(finalAttackRange, finalAttackRange, finalAttackRange)) {
                    if (entity instanceof Player) {
                        double distance = creeper.getLocation().distance(entity.getLocation());
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            target = (Player) entity;
                        }
                    }
                }

                if (target != null) {
                    final Player finalTarget = target;
                    Location targetLocation = finalTarget.getLocation();

                    // 在目标玩家位置生成闪电
                    creeper.getWorld().strikeLightning(targetLocation);

                    // 播放音效
                    creeper.getWorld().playSound(creeper.getLocation(), org.bukkit.Sound.ENTITY_CREEPER_PRIMED, 1.0f, 0.8f);

                    if (debugMode) {
                        logger.info("变异爬行者对玩家 " + finalTarget.getName() + " 释放了闪电攻击");
                    }
                }
            }
        }.runTaskTimer(plugin, 60, attackInterval); // 3秒后开始，按配置间隔执行

        // 保存任务以便后续取消
        particleEffectTasks.put(creeper, lightningTask);

        if (debugMode) {
            logger.info("为变异爬行者启动闪电攻击任务，间隔: " + attackInterval + "tick，范围: " + attackRange + "格");
        }
    }

    /**
     * 启动变异僵尸02的螺旋紫色粒子效果
     */
    private void startMutantZombie02ParticleEffect(LivingEntity entity, EntityOverrideConfig config) {
        // 从配置中获取粒子效果参数
        int particleInterval = 5; // 默认每5tick执行一次
        if (config.specialAbilities.containsKey("particle_interval")) {
            particleInterval = (Integer) config.specialAbilities.get("particle_interval");
        }

        BukkitTask particleTask = new BukkitRunnable() {
            double time = 0;

            @Override
            public void run() {
                if (entity == null || entity.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(entity);
                    return;
                }

                // 创建螺旋上升的紫色粒子效果
                Location baseLocation = entity.getLocation().add(0, 0.5, 0);
                time += 0.3;

                for (double y = 0; y < 2; y += 0.2) {
                    double radius = 0.8 - y * 0.2;
                    double x = Math.cos(time + y * 3) * radius;
                    double z = Math.sin(time + y * 3) * radius;

                    Location particleLoc = baseLocation.clone().add(x, y, z);

                    // 生成紫色粒子
                    entity.getWorld().spawnParticle(
                        org.bukkit.Particle.DUST,
                        particleLoc,
                        1, 0, 0, 0, 0,
                        new org.bukkit.Particle.DustOptions(org.bukkit.Color.PURPLE, 1.0f)
                    );
                }
            }
        }.runTaskTimer(plugin, 5, particleInterval); // 5tick延迟开始，按配置间隔执行

        // 保存任务以便后续取消
        particleEffectTasks.put(entity, particleTask);

        if (debugMode) {
            logger.info("为变异僵尸02启动螺旋紫色粒子效果任务，间隔: " + particleInterval + "tick");
        }
    }

    /**
     * 启动变异僵尸01的粒子效果
     */
    private void startMutantZombie01ParticleEffect(LivingEntity entity, EntityOverrideConfig config) {
        // 从配置中获取粒子效果参数
        int particleInterval = 5; // 默认每5tick执行一次
        if (config.specialAbilities.containsKey("particle_interval")) {
            particleInterval = (Integer) config.specialAbilities.get("particle_interval");
        }

        BukkitTask particleTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (entity == null || entity.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(entity);
                    return;
                }

                // 生成黑色烟雾粒子围成的正方体"气场"
                Location location = entity.getLocation();

                // 底部边缘
                for (double x = -1; x <= 1; x += 0.25) {
                    for (double z = -1; z <= 1; z += 0.25) {
                        if (Math.abs(x) > 0.9 || Math.abs(z) > 0.9) { // 只在边缘生成
                            Location particleLoc = location.clone().add(x, 0, z);
                            particleHelper.displaySmokeParticle(
                                org.bukkit.Bukkit.getOnlinePlayers(),
                                particleLoc, 0, 0, 0, 0, 1
                            );
                        }
                    }
                }

                // 顶部边缘
                for (double x = -1; x <= 1; x += 0.25) {
                    for (double z = -1; z <= 1; z += 0.25) {
                        if (Math.abs(x) > 0.9 || Math.abs(z) > 0.9) { // 只在边缘生成
                            Location particleLoc = location.clone().add(x, 2, z);
                            particleHelper.displaySmokeParticle(
                                org.bukkit.Bukkit.getOnlinePlayers(),
                                particleLoc, 0, 0, 0, 0, 1
                            );
                        }
                    }
                }

                // 四个立柱
                for (double y = 0; y <= 2; y += 0.25) {
                    Location corner1 = location.clone().add(-1, y, -1);
                    Location corner2 = location.clone().add(-1, y, 1);
                    Location corner3 = location.clone().add(1, y, -1);
                    Location corner4 = location.clone().add(1, y, 1);

                    particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner1, 0, 0, 0, 0, 1);
                    particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner2, 0, 0, 0, 0, 1);
                    particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner3, 0, 0, 0, 0, 1);
                    particleHelper.displaySmokeParticle(org.bukkit.Bukkit.getOnlinePlayers(), corner4, 0, 0, 0, 0, 1);
                }
            }
        }.runTaskTimer(plugin, 0, particleInterval); // 立即开始，按配置间隔执行

        // 保存任务以便后续取消
        particleEffectTasks.put(entity, particleTask);

        if (debugMode) {
            logger.info("为变异僵尸01启动粒子效果任务，间隔: " + particleInterval + "tick");
        }
    }

    /**
     * 清理实体的粒子效果任务
     */
    private void cleanupParticleEffectTask(LivingEntity entity) {
        BukkitTask task = particleEffectTasks.remove(entity);
        if (task != null && !task.isCancelled()) {
            task.cancel();
            if (debugMode) {
                logger.info("已清理实体的粒子效果任务");
            }
        }
    }

    /**
     * 清理所有粒子效果任务
     */
    public void cleanupAllParticleEffectTasks() {
        for (BukkitTask task : particleEffectTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        particleEffectTasks.clear();

        if (debugMode) {
            logger.info("已清理所有粒子效果任务");
        }
    }

    /**
     * 启用变异末影螨的技能（电流攻击、传送、分裂、粒子效果）
     */
    private void enableMutantEndermiteSkills(org.bukkit.entity.Endermite endermite, EntityOverrideConfig config) {
        // 启用电流攻击技能
        boolean electricEnabled = true;
        if (config.specialAbilities.containsKey("electric_attack_enabled")) {
            electricEnabled = (Boolean) config.specialAbilities.get("electric_attack_enabled");
        }

        // 启用传送技能
        boolean teleportEnabled = true;
        if (config.specialAbilities.containsKey("teleport_enabled")) {
            teleportEnabled = (Boolean) config.specialAbilities.get("teleport_enabled");
        }

        // 启用分裂技能
        boolean cloneEnabled = true;
        if (config.specialAbilities.containsKey("clone_enabled")) {
            cloneEnabled = (Boolean) config.specialAbilities.get("clone_enabled");
        }

        // 启用粒子效果
        boolean particleEnabled = true;
        if (config.specialAbilities.containsKey("particle_enabled")) {
            particleEnabled = (Boolean) config.specialAbilities.get("particle_enabled");
        }

        // 启动综合技能系统
        startMutantEndermiteAllSkills(endermite, config, electricEnabled, teleportEnabled, cloneEnabled, particleEnabled);

        if (debugMode) {
            logger.info("为变异末影螨启用技能 - 电流攻击: " + electricEnabled +
                       ", 传送: " + teleportEnabled +
                       ", 分裂: " + cloneEnabled +
                       ", 粒子效果: " + particleEnabled);
        }
    }

    /**
     * 启动变异末影螨的综合技能系统（电流攻击、传送、分裂、粒子效果）
     */
    private void startMutantEndermiteAllSkills(org.bukkit.entity.Endermite endermite, EntityOverrideConfig config,
                                               boolean electricEnabled, boolean teleportEnabled,
                                               boolean cloneEnabled, boolean particleEnabled) {

        // 从配置中获取各种参数
        final double electricDamage = config.specialAbilities.containsKey("electric_damage") ?
            ((Number) config.specialAbilities.get("electric_damage")).doubleValue() : 4.0;
        final int electricRange = config.specialAbilities.containsKey("electric_range") ?
            (Integer) config.specialAbilities.get("electric_range") : 10;
        final int electricInterval = config.specialAbilities.containsKey("electric_interval") ?
            (Integer) config.specialAbilities.get("electric_interval") : 60;

        final int teleportInterval = config.specialAbilities.containsKey("teleport_interval") ?
            (Integer) config.specialAbilities.get("teleport_interval") : 8000; // 8秒
        final int teleportRange = config.specialAbilities.containsKey("teleport_range") ?
            (Integer) config.specialAbilities.get("teleport_range") : 20;

        final int cloneInterval = config.specialAbilities.containsKey("clone_interval") ?
            (Integer) config.specialAbilities.get("clone_interval") : 25000; // 25秒
        final int maxClones = config.specialAbilities.containsKey("max_clones") ?
            (Integer) config.specialAbilities.get("max_clones") : 5;

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;
            private long lastTeleportTime = System.currentTimeMillis();
            private long lastCloneTime = System.currentTimeMillis();
            private int electricTickCounter = 0;

            @Override
            public void run() {
                if (endermite == null || endermite.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(endermite);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                Location location = endermite.getLocation();

                // 1. 电流攻击（每配置间隔执行）
                if (electricEnabled && electricTickCounter >= electricInterval) {
                    performElectricAttack(endermite, location, electricDamage, electricRange);
                    electricTickCounter = 0;
                }

                // 2. 随机传送（每8秒）
                if (teleportEnabled && currentTime - lastTeleportTime >= teleportInterval) {
                    performTeleport(endermite, teleportRange);
                    lastTeleportTime = currentTime;
                }

                // 3. 分裂召唤（每25秒）
                if (cloneEnabled && currentTime - lastCloneTime >= cloneInterval) {
                    performClone(endermite, maxClones);
                    lastCloneTime = currentTime;
                }

                // 4. 持续粒子效果（每0.5秒）
                if (particleEnabled && tickCounter % 10 == 0) {
                    performParticleEffects(endermite, location);
                }

                tickCounter++;
                electricTickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(endermite, skillTask);

        if (debugMode) {
            logger.info("为变异末影螨启动综合技能系统");
        }
    }

    /**
     * 执行电流攻击
     */
    private void performElectricAttack(org.bukkit.entity.Endermite endermite, Location location,
                                       double electricDamage, int electricRange) {
        // 生成电流粒子效果
        location.getWorld().spawnParticle(
            org.bukkit.Particle.ENCHANTED_HIT,
            location,
            50,
            electricRange / 2.0,
            1.0,
            electricRange / 2.0,
            1.0
        );

        // 对范围内的玩家造成伤害
        for (org.bukkit.entity.Entity entity : endermite.getNearbyEntities(electricRange, electricRange, electricRange)) {
            if (entity instanceof org.bukkit.entity.Player) {
                org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;

                // 造成电流伤害
                player.damage(electricDamage, endermite);

                // 添加中毒效果（3秒）
                player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.POISON, 60, 1));

                if (debugMode) {
                    logger.info("变异末影螨对玩家 " + player.getName() + " 造成了 " + electricDamage + " 点电流伤害");
                }
            }
        }

        // 播放电击音效
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.5f, 2.0f);
    }

    /**
     * 执行随机传送
     */
    private void performTeleport(org.bukkit.entity.Endermite endermite, int teleportRange) {
        // 找到范围内的玩家
        java.util.List<org.bukkit.entity.Player> nearbyPlayers = new java.util.ArrayList<>();
        for (org.bukkit.entity.Entity entity : endermite.getNearbyEntities(teleportRange, teleportRange, teleportRange)) {
            if (entity instanceof org.bukkit.entity.Player) {
                nearbyPlayers.add((org.bukkit.entity.Player) entity);
            }
        }

        if (!nearbyPlayers.isEmpty()) {
            // 随机选择一个玩家
            org.bukkit.entity.Player target = nearbyPlayers.get(new java.util.Random().nextInt(nearbyPlayers.size()));
            Location targetLoc = target.getLocation();

            // 在玩家周围随机位置传送
            double offsetX = (Math.random() - 0.5) * 5;
            double offsetZ = (Math.random() - 0.5) * 5;
            Location teleportLoc = targetLoc.clone().add(offsetX, 0, offsetZ);

            // 传送前粒子效果
            endermite.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, endermite.getLocation(), 30, 0.5, 1.0, 0.5, 0.1);

            // 播放传送音效
            endermite.getWorld().playSound(endermite.getLocation(), org.bukkit.Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);

            // 传送末影螨
            endermite.teleport(teleportLoc);

            // 传送后粒子效果
            endermite.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, teleportLoc, 30, 0.5, 1.0, 0.5, 0.1);

            if (debugMode) {
                logger.info("变异末影螨传送到玩家 " + target.getName() + " 附近");
            }
        }
    }

    /**
     * 执行分裂召唤
     */
    private void performClone(org.bukkit.entity.Endermite endermite, int maxClones) {
        // 检查周围末影螨的数量
        int endermiteCount = 0;
        for (org.bukkit.entity.Entity entity : endermite.getNearbyEntities(10, 10, 10)) {
            if (entity instanceof org.bukkit.entity.Endermite) {
                endermiteCount++;
            }
        }

        // 如果周围末影螨数量少于最大值，可以分裂
        if (endermiteCount < maxClones) {
            // 在末影螨位置生成一个普通末影螨
            org.bukkit.entity.Endermite newEndermite = (org.bukkit.entity.Endermite)
                endermite.getWorld().spawnEntity(endermite.getLocation(), org.bukkit.entity.EntityType.ENDERMITE);

            // 设置新末影螨属性
            newEndermite.setCustomName("§5末影螨仆从");
            newEndermite.setCustomNameVisible(true);
            newEndermite.setMaxHealth(30.0);
            newEndermite.setHealth(30.0);

            // 添加速度效果
            newEndermite.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.SPEED, Integer.MAX_VALUE, 2)); // 速度3

            // 添加标记，确保被识别为游戏实体
            newEndermite.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            newEndermite.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));

            // 分裂效果
            endermite.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH,
                endermite.getLocation(), 30, 0.5, 1.0, 0.5, 0.1);

            // 播放分裂音效
            endermite.getWorld().playSound(endermite.getLocation(), org.bukkit.Sound.ENTITY_SLIME_SQUISH, 1.0f, 1.0f);

            if (debugMode) {
                logger.info("变异末影螨分裂出一个仆从，当前数量: " + (endermiteCount + 1));
            }
        }
    }

    /**
     * 执行持续粒子效果
     */
    private void performParticleEffects(org.bukkit.entity.Endermite endermite, Location location) {
        // 在末影螨周围生成末影粒子
        location.getWorld().spawnParticle(org.bukkit.Particle.PORTAL, location, 5, 0.3, 0.3, 0.3, 0.1);

        // 在末影螨顶部生成龙息粒子
        Location topLocation = location.clone().add(0, 0.5, 0);
        location.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, topLocation, 3, 0.2, 0.2, 0.2, 0.01);
    }

    /**
     * 启用变异蜘蛛的技能（蜘蛛网生成、毒液喷射、跳跃攻击、蜘蛛网攻击、粒子效果）
     */
    private void enableMutantSpiderSkills(org.bukkit.entity.CaveSpider spider, EntityOverrideConfig config) {
        // 启用蜘蛛网生成技能
        boolean webGenerationEnabled = true;
        if (config.specialAbilities.containsKey("web_generation_enabled")) {
            webGenerationEnabled = (Boolean) config.specialAbilities.get("web_generation_enabled");
        }

        // 启用毒液喷射技能
        boolean poisonSprayEnabled = true;
        if (config.specialAbilities.containsKey("poison_spray_enabled")) {
            poisonSprayEnabled = (Boolean) config.specialAbilities.get("poison_spray_enabled");
        }

        // 启用跳跃攻击技能
        boolean jumpAttackEnabled = true;
        if (config.specialAbilities.containsKey("jump_attack_enabled")) {
            jumpAttackEnabled = (Boolean) config.specialAbilities.get("jump_attack_enabled");
        }

        // 启用蜘蛛网攻击技能
        boolean webAttackEnabled = true;
        if (config.specialAbilities.containsKey("web_attack_enabled")) {
            webAttackEnabled = (Boolean) config.specialAbilities.get("web_attack_enabled");
        }

        // 启用粒子效果
        boolean particleEnabled = true;
        if (config.specialAbilities.containsKey("particle_enabled")) {
            particleEnabled = (Boolean) config.specialAbilities.get("particle_enabled");
        }

        // 启动综合技能系统
        startMutantSpiderAllSkills(spider, config, webGenerationEnabled, poisonSprayEnabled,
                                   jumpAttackEnabled, webAttackEnabled, particleEnabled);

        if (debugMode) {
            logger.info("为变异蜘蛛启用技能 - 蜘蛛网生成: " + webGenerationEnabled +
                       ", 毒液喷射: " + poisonSprayEnabled +
                       ", 跳跃攻击: " + jumpAttackEnabled +
                       ", 蜘蛛网攻击: " + webAttackEnabled +
                       ", 粒子效果: " + particleEnabled);
        }
    }

    /**
     * 启动变异蜘蛛的综合技能系统（五大技能）
     */
    private void startMutantSpiderAllSkills(org.bukkit.entity.CaveSpider spider, EntityOverrideConfig config,
                                            boolean webGenerationEnabled, boolean poisonSprayEnabled,
                                            boolean jumpAttackEnabled, boolean webAttackEnabled,
                                            boolean particleEnabled) {

        // 从配置中获取各种参数
        final int webGenerationInterval = config.specialAbilities.containsKey("web_generation_interval") ?
            (Integer) config.specialAbilities.get("web_generation_interval") : 10000; // 10秒
        final int webGenerationCount = config.specialAbilities.containsKey("web_generation_count") ?
            (Integer) config.specialAbilities.get("web_generation_count") : 5;
        final int webGenerationDuration = config.specialAbilities.containsKey("web_generation_duration") ?
            (Integer) config.specialAbilities.get("web_generation_duration") : 600; // 30秒

        final int poisonSprayInterval = config.specialAbilities.containsKey("poison_spray_interval") ?
            (Integer) config.specialAbilities.get("poison_spray_interval") : 15000; // 15秒
        final int poisonSprayRange = config.specialAbilities.containsKey("poison_spray_range") ?
            (Integer) config.specialAbilities.get("poison_spray_range") : 10;
        final int poisonLevel = config.specialAbilities.containsKey("poison_level") ?
            (Integer) config.specialAbilities.get("poison_level") : 4; // 中毒V
        final int poisonDuration = config.specialAbilities.containsKey("poison_duration") ?
            (Integer) config.specialAbilities.get("poison_duration") : 600; // 30秒

        final int jumpAttackInterval = config.specialAbilities.containsKey("jump_attack_interval") ?
            (Integer) config.specialAbilities.get("jump_attack_interval") : 5000; // 5秒
        final int jumpAttackRange = config.specialAbilities.containsKey("jump_attack_range") ?
            (Integer) config.specialAbilities.get("jump_attack_range") : 15;

        final int webAttackInterval = config.specialAbilities.containsKey("web_attack_interval") ?
            (Integer) config.specialAbilities.get("web_attack_interval") : 4000; // 4秒
        final int webAttackRange = config.specialAbilities.containsKey("web_attack_range") ?
            (Integer) config.specialAbilities.get("web_attack_range") : 15;
        final int webAttackDuration = config.specialAbilities.containsKey("web_attack_duration") ?
            (Integer) config.specialAbilities.get("web_attack_duration") : 100; // 5秒

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;
            private long lastWebGenerationTime = System.currentTimeMillis();
            private long lastPoisonSprayTime = System.currentTimeMillis();
            private long lastJumpAttackTime = System.currentTimeMillis();
            private long lastWebAttackTime = System.currentTimeMillis();

            @Override
            public void run() {
                if (spider == null || spider.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(spider);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                Location location = spider.getLocation();

                // 1. 蜘蛛网生成（每10秒）
                if (webGenerationEnabled && currentTime - lastWebGenerationTime >= webGenerationInterval) {
                    performWebGeneration(spider, location, webGenerationCount, webGenerationDuration);
                    lastWebGenerationTime = currentTime;
                }

                // 2. 毒液喷射（每15秒）
                if (poisonSprayEnabled && currentTime - lastPoisonSprayTime >= poisonSprayInterval) {
                    performPoisonSpray(spider, poisonSprayRange, poisonLevel, poisonDuration);
                    lastPoisonSprayTime = currentTime;
                }

                // 3. 跳跃攻击（每5秒）
                if (jumpAttackEnabled && currentTime - lastJumpAttackTime >= jumpAttackInterval) {
                    performJumpAttack(spider, jumpAttackRange);
                    lastJumpAttackTime = currentTime;
                }

                // 4. 蜘蛛网攻击（每4秒）
                if (webAttackEnabled && currentTime - lastWebAttackTime >= webAttackInterval) {
                    performWebAttack(spider, webAttackRange, webAttackDuration);
                    lastWebAttackTime = currentTime;
                }

                // 5. 持续粒子效果（每0.5秒）
                if (particleEnabled && tickCounter % 10 == 0) {
                    performSpiderParticleEffects(spider, location);
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(spider, skillTask);

        if (debugMode) {
            logger.info("为变异蜘蛛启动综合技能系统");
        }
    }

    /**
     * 执行蜘蛛网生成
     */
    private void performWebGeneration(org.bukkit.entity.CaveSpider spider, Location location, int webCount, int duration) {
        // 在蜘蛛周围5×5×5的范围内随机位置生成蜘蛛网
        for (int i = 0; i < webCount; i++) {
            // 随机偏移
            double offsetX = (Math.random() - 0.5) * 10;
            double offsetY = (Math.random() - 0.5) * 2;
            double offsetZ = (Math.random() - 0.5) * 10;

            Location webLoc = location.clone().add(offsetX, offsetY, offsetZ);

            // 确保位置有效且是空气方块
            if (webLoc.getBlock().getType() == org.bukkit.Material.AIR) {
                webLoc.getBlock().setType(org.bukkit.Material.COBWEB);

                // 生成蜘蛛网粒子效果
                spider.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, webLoc, 10, 0.5, 0.5, 0.5, 0.1);

                // 配置时间后自动移除蜘蛛网（防止地图被永久污染）
                final Location finalWebLoc = webLoc.clone();
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (finalWebLoc.getBlock().getType() == org.bukkit.Material.COBWEB) {
                            finalWebLoc.getBlock().setType(org.bukkit.Material.AIR);

                            // 移除时的粒子效果
                            spider.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, finalWebLoc, 5, 0.2, 0.2, 0.2, 0.01);

                            if (debugMode) {
                                logger.info("自动移除了一个过期的蜘蛛网");
                            }
                        }
                    }
                }.runTaskLater(plugin, duration); // 配置的持续时间后执行
            }
        }

        // 播放蜘蛛网生成音效
        spider.getWorld().playSound(location, org.bukkit.Sound.BLOCK_STONE_PLACE, 1.0f, 1.0f);

        if (debugMode) {
            logger.info("变异蜘蛛生成了 " + webCount + " 个蜘蛛网（" + (duration/20) + "秒后自动移除）");
        }
    }

    /**
     * 执行毒液喷射
     */
    private void performPoisonSpray(org.bukkit.entity.CaveSpider spider, int range, int poisonLevel, int duration) {
        // 找到范围内的玩家
        java.util.List<org.bukkit.entity.Player> nearbyPlayers = new java.util.ArrayList<>();
        for (org.bukkit.entity.Entity entity : spider.getNearbyEntities(range, range, range)) {
            if (entity instanceof org.bukkit.entity.Player) {
                nearbyPlayers.add((org.bukkit.entity.Player) entity);
            }
        }

        if (!nearbyPlayers.isEmpty()) {
            for (org.bukkit.entity.Player player : nearbyPlayers) {
                // 给玩家添加超强毒液效果
                player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.POISON, duration, poisonLevel));

                // 显示毒液效果
                Location playerLoc = player.getLocation().add(0, 1, 0);
                spider.getWorld().spawnParticle(org.bukkit.Particle.SNEEZE, playerLoc, 20, 0.5, 1.0, 0.5, 0.1);

                // 不发送消息（避免刷屏）
                // player.sendMessage("§c你被变异蜘蛛的毒液击中了！");

                if (debugMode) {
                    logger.info("变异蜘蛛对玩家 " + player.getName() + " 喷射了毒液");
                }
            }

            // 播放毒液喷射音效
            spider.getWorld().playSound(spider.getLocation(), org.bukkit.Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 0.5f);
        }
    }

    /**
     * 执行跳跃攻击
     */
    private void performJumpAttack(org.bukkit.entity.CaveSpider spider, int range) {
        // 找到范围内最近的玩家
        org.bukkit.entity.Player nearestPlayer = null;
        double nearestDistance = range + 1;

        for (org.bukkit.entity.Entity entity : spider.getNearbyEntities(range, range, range)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = spider.getLocation().distance(entity.getLocation());
                if (distance < nearestDistance && distance > 3.0) { // 确保玩家不太近
                    nearestDistance = distance;
                    nearestPlayer = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (nearestPlayer != null) {
            // 获取玩家位置
            Location playerLoc = nearestPlayer.getLocation();

            // 计算蜘蛛到玩家的方向向量
            org.bukkit.util.Vector direction = playerLoc.toVector().subtract(spider.getLocation().toVector()).normalize();

            // 增加跳跃强度
            direction.multiply(1.5).setY(0.8); // 水平速度1.5，垂直跳跃0.8

            // 使蜘蛛跳向玩家
            spider.setVelocity(direction);

            // 播放跳跃音效
            spider.getWorld().playSound(spider.getLocation(), org.bukkit.Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 1.5f);

            // 跳跃粒子效果
            spider.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, spider.getLocation(), 10, 0.3, 0.1, 0.3, 0.05);

            if (debugMode) {
                logger.info("变异蜘蛛跳向玩家 " + nearestPlayer.getName());
            }
        }
    }

    /**
     * 执行蜘蛛网攻击
     */
    private void performWebAttack(org.bukkit.entity.CaveSpider spider, int range, int duration) {
        // 查找范围内最近的玩家
        org.bukkit.entity.Player target = null;
        double closestDistance = range + 1;

        for (org.bukkit.entity.Entity entity : spider.getNearbyEntities(range, range, range)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = spider.getLocation().distance(entity.getLocation());
                if (distance < closestDistance) {
                    closestDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            Location targetLoc = target.getLocation();

            // 在目标玩家脚下生成蜘蛛网
            if (targetLoc.getBlock().getType() == org.bukkit.Material.AIR) {
                targetLoc.getBlock().setType(org.bukkit.Material.COBWEB);

                // 配置时间后移除蜘蛛网（改进版本，增加安全检查）
                final Location finalTargetLoc = targetLoc.clone();
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        // 检查方块是否仍然是蜘蛛网
                        if (finalTargetLoc.getBlock().getType() == org.bukkit.Material.COBWEB) {
                            finalTargetLoc.getBlock().setType(org.bukkit.Material.AIR);

                            // 移除时的粒子效果
                            spider.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, finalTargetLoc, 5, 0.2, 0.2, 0.2, 0.01);

                            if (debugMode) {
                                logger.info("移除了蜘蛛网攻击生成的蜘蛛网");
                            }
                        }
                    }
                }.runTaskLater(plugin, duration); // 配置的持续时间后执行
            }

            // 给玩家添加缓慢效果
            target.addPotionEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.SLOWNESS, 100, 2)); // 5秒缓慢III

            // 显示蜘蛛网粒子效果
            spider.getWorld().spawnParticle(org.bukkit.Particle.ITEM, targetLoc, 20, 0.5, 0.5, 0.5, 0.1,
                new org.bukkit.inventory.ItemStack(org.bukkit.Material.COBWEB));

            // 播放蜘蛛攻击音效
            spider.getWorld().playSound(spider.getLocation(), org.bukkit.Sound.ENTITY_SPIDER_AMBIENT, 1.0f, 0.8f);

            if (debugMode) {
                logger.info("变异蜘蛛对玩家 " + target.getName() + " 使用了蜘蛛网攻击");
            }
        }
    }

    /**
     * 执行蜘蛛持续粒子效果
     */
    private void performSpiderParticleEffects(org.bukkit.entity.CaveSpider spider, Location location) {
        // 在蜘蛛周围生成绿色粒子（表示毒性）
        for (int i = 0; i < 3; i++) {
            double offsetX = (Math.random() - 0.5) * 2;
            double offsetY = (Math.random() - 0.5) + 0.5; // 偏向上方
            double offsetZ = (Math.random() - 0.5) * 2;

            Location particleLoc = location.clone().add(offsetX, offsetY, offsetZ);
            spider.getWorld().spawnParticle(org.bukkit.Particle.HAPPY_VILLAGER, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 启用灾厄卫道士的技能（脚底暴击圆圈、召唤卫道士、发射伤害球体）
     */
    private void enableDisasterGuardianSkills(org.bukkit.entity.Pillager pillager, EntityOverrideConfig config) {
        // 启用脚底暴击圆圈技能
        boolean circleAttackEnabled = true;
        if (config.specialAbilities.containsKey("circle_attack_enabled")) {
            circleAttackEnabled = (Boolean) config.specialAbilities.get("circle_attack_enabled");
        }

        // 启用召唤卫道士技能
        boolean summonEnabled = true;
        if (config.specialAbilities.containsKey("summon_enabled")) {
            summonEnabled = (Boolean) config.specialAbilities.get("summon_enabled");
        }

        // 启用发射伤害球体技能
        boolean projectileEnabled = true;
        if (config.specialAbilities.containsKey("projectile_enabled")) {
            projectileEnabled = (Boolean) config.specialAbilities.get("projectile_enabled");
        }

        // 启动综合技能系统
        startDisasterGuardianAllSkills(pillager, config, circleAttackEnabled, summonEnabled, projectileEnabled);

        if (debugMode) {
            logger.info("为灾厄卫道士启用技能 - 脚底暴击圆圈: " + circleAttackEnabled +
                       ", 召唤卫道士: " + summonEnabled +
                       ", 发射伤害球体: " + projectileEnabled);
        }
    }

    /**
     * 启动灾厄卫道士的综合技能系统（三大技能）
     */
    private void startDisasterGuardianAllSkills(org.bukkit.entity.Pillager pillager, EntityOverrideConfig config,
                                                boolean circleAttackEnabled, boolean summonEnabled,
                                                boolean projectileEnabled) {

        // 从配置中获取各种参数
        final double circleRadius = config.specialAbilities.containsKey("circle_radius") ?
            ((Number) config.specialAbilities.get("circle_radius")).doubleValue() : 3.0;
        final double circleDamage = config.specialAbilities.containsKey("circle_damage") ?
            ((Number) config.specialAbilities.get("circle_damage")).doubleValue() : 2.0;

        final int summonInterval = config.specialAbilities.containsKey("summon_interval") ?
            (Integer) config.specialAbilities.get("summon_interval") : 5000; // 5秒
        final int maxMinions = config.specialAbilities.containsKey("max_minions") ?
            (Integer) config.specialAbilities.get("max_minions") : 10;

        final int projectileInterval = config.specialAbilities.containsKey("projectile_interval") ?
            (Integer) config.specialAbilities.get("projectile_interval") : 4000; // 4秒
        final double projectileDamage = config.specialAbilities.containsKey("projectile_damage") ?
            ((Number) config.specialAbilities.get("projectile_damage")).doubleValue() : 10.0;

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;
            private long lastSummonTime = System.currentTimeMillis();
            private long lastProjectileTime = System.currentTimeMillis();

            @Override
            public void run() {
                if (pillager == null || pillager.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(pillager);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                Location location = pillager.getLocation();

                // 1. 脚底暴击粒子圆圈（每0.5秒）
                if (circleAttackEnabled && tickCounter % 10 == 0) {
                    performCircleAttack(pillager, location, circleRadius, circleDamage);
                }

                // 2. 召唤卫道士（每5秒）
                if (summonEnabled && currentTime - lastSummonTime >= summonInterval) {
                    performSummonMinions(pillager, maxMinions);
                    lastSummonTime = currentTime;
                }

                // 3. 发射伤害球体（每4秒）
                if (projectileEnabled && currentTime - lastProjectileTime >= projectileInterval) {
                    performProjectileAttack(pillager, projectileDamage);
                    lastProjectileTime = currentTime;
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(pillager, skillTask);

        if (debugMode) {
            logger.info("为灾厄卫道士启动综合技能系统");
        }
    }

    /**
     * 执行脚底暴击圆圈攻击
     */
    private void performCircleAttack(org.bukkit.entity.Pillager pillager, Location location, double radius, double damage) {
        // 创建脚底暴击粒子圆圈
        for (int i = 0; i < 48; i++) {
            double angle = 2 * Math.PI * i / 48;
            double x = location.getX() + radius * Math.cos(angle);
            double z = location.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(location.getWorld(), x, location.getY(), z);

            // 生成暴击粒子
            location.getWorld().spawnParticle(org.bukkit.Particle.CRIT, particleLoc, 1, 0, 0, 0, 0);
        }

        // 检查圆圈范围内的玩家
        for (org.bukkit.entity.Entity entity : pillager.getNearbyEntities(radius, radius, radius)) {
            if (entity instanceof org.bukkit.entity.Player) {
                org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;
                Location playerLoc = player.getLocation();

                // 计算水平距离
                double dx = playerLoc.getX() - location.getX();
                double dz = playerLoc.getZ() - location.getZ();
                double distance = Math.sqrt(dx * dx + dz * dz);

                // 如果玩家在圆圈范围内（水平距离小于等于半径，且高度差不超过1.5）
                if (distance <= radius && Math.abs(playerLoc.getY() - location.getY()) <= 1.5) {
                    // 造成伤害
                    player.damage(damage, pillager);

                    // 播放受伤音效
                    player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 0.5f, 1.0f);

                    // 显示受伤粒子
                    player.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR,
                        player.getLocation().add(0, 1, 0), 5, 0.5, 0.5, 0.5, 0);

                    if (debugMode) {
                        logger.info("灾厄卫道士的脚底暴击圆圈对玩家 " + player.getName() + " 造成了 " + damage + " 点伤害");
                    }
                }
            }
        }
    }

    /**
     * 执行召唤卫道士仆从
     */
    private void performSummonMinions(org.bukkit.entity.Pillager pillager, int maxMinions) {
        Location location = pillager.getLocation();

        // 检查周围已有的卫道士数量
        int nearbyVindicators = 0;
        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 20, 20, 20)) {
            if (entity instanceof org.bukkit.entity.Vindicator && entity.hasMetadata("pillagerMinion")) {
                nearbyVindicators++;
            }
        }

        // 如果卫道士数量少于最大值，召唤新的
        if (nearbyVindicators < maxMinions) {
            int toSummon = Math.min(2, maxMinions - nearbyVindicators); // 每次最多召唤2个

            for (int i = 0; i < toSummon; i++) {
                // 在周围随机位置召唤卫道士
                double offsetX = (Math.random() - 0.5) * 6;
                double offsetZ = (Math.random() - 0.5) * 6;
                Location summonLoc = location.clone().add(offsetX, 0, offsetZ);

                // 生成卫道士
                org.bukkit.entity.Vindicator minion = (org.bukkit.entity.Vindicator)
                    location.getWorld().spawnEntity(summonLoc, org.bukkit.entity.EntityType.VINDICATOR);

                // 设置卫道士属性
                minion.setCustomName("§4卫道士仆从");
                minion.setCustomNameVisible(true);

                // 添加速度效果
                minion.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II

                // 添加元数据标记为卫道士仆从
                minion.setMetadata("pillagerMinion", new FixedMetadataValue(plugin, true));
                minion.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            }

            // 播放召唤效果
            location.getWorld().spawnParticle(org.bukkit.Particle.SOUL_FIRE_FLAME,
                location.clone().add(0, 1, 0), 20, 1, 1, 1, 0.1);

            if (debugMode) {
                logger.info("灾厄卫道士召唤了 " + toSummon + " 个卫道士仆从，当前总数: " + (nearbyVindicators + toSummon));
            }
        }
    }

    /**
     * 执行发射伤害球体攻击
     */
    private void performProjectileAttack(org.bukkit.entity.Pillager pillager, double damage) {
        Location location = pillager.getLocation().add(0, 1.0, 0); // 从掠夺者中心位置发射

        // 寻找最近的玩家
        org.bukkit.entity.Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (org.bukkit.entity.Entity entity : pillager.getNearbyEntities(30, 30, 30)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = location.distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            final org.bukkit.entity.Player finalTarget = target;
            final Location startLoc = location.clone();
            final Location targetLoc = target.getLocation().add(0, 1, 0);

            // 计算方向向量
            org.bukkit.util.Vector direction = targetLoc.toVector().subtract(startLoc.toVector()).normalize();

            // 创建球体移动任务
            new BukkitRunnable() {
                private Location currentLoc = startLoc.clone();
                private int ticks = 0;
                private final int maxTicks = 100; // 最多飞行5秒

                @Override
                public void run() {
                    if (pillager == null || pillager.isDead() || ticks >= maxTicks) {
                        this.cancel();
                        return;
                    }

                    // 移动球体
                    currentLoc.add(direction.clone().multiply(0.5));

                    // 生成红+黑+白色球体环绕粒子
                    try {
                        // 尝试使用DUST粒子（红色）
                        currentLoc.getWorld().spawnParticle(org.bukkit.Particle.DUST, currentLoc, 3, 0.2, 0.2, 0.2, 0,
                            new org.bukkit.Particle.DustOptions(org.bukkit.Color.RED, 1.0f));
                    } catch (Exception e) {
                        // 如果DUST不可用，使用FLAME作为替代
                        currentLoc.getWorld().spawnParticle(org.bukkit.Particle.FLAME, currentLoc, 3, 0.2, 0.2, 0.2, 0.01);
                    }
                    currentLoc.getWorld().spawnParticle(org.bukkit.Particle.SMOKE, currentLoc, 2, 0.1, 0.1, 0.1, 0.01);
                    currentLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, currentLoc, 1, 0.1, 0.1, 0.1, 0);

                    // 检查是否击中玩家
                    for (org.bukkit.entity.Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1, 1, 1)) {
                        if (entity instanceof org.bukkit.entity.Player) {
                            org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;

                            // 造成巨量伤害
                            player.damage(damage, pillager);

                            // 击退效果
                            org.bukkit.util.Vector knockback = player.getLocation().subtract(currentLoc).toVector().normalize().multiply(1.5);
                            player.setVelocity(knockback);

                            // 爆炸效果
                            currentLoc.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, currentLoc, 5, 1, 1, 1, 0);
                            currentLoc.getWorld().playSound(currentLoc, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.0f);

                            if (debugMode) {
                                logger.info("灾厄卫道士的伤害球体击中玩家 " + player.getName() + "，造成 " + damage + " 点伤害");
                            }

                            this.cancel();
                            return;
                        }
                    }

                    ticks++;
                }
            }.runTaskTimer(plugin, 0, 1);

            if (debugMode) {
                logger.info("灾厄卫道士向玩家 " + target.getName() + " 发射了伤害球体");
            }
        }
    }

    /**
     * 启用灾厄唤魔者的技能（脚底粒子圆圈、召唤卫道士、DNA螺旋魔法攻击）
     */
    private void enableDisasterSummonerSkills(org.bukkit.entity.Evoker evoker, EntityOverrideConfig config) {
        // 启用脚底粒子圆圈技能
        boolean circleEnabled = true;
        if (config.specialAbilities.containsKey("circle_enabled")) {
            circleEnabled = (Boolean) config.specialAbilities.get("circle_enabled");
        }

        // 启用召唤卫道士技能
        boolean summonEnabled = true;
        if (config.specialAbilities.containsKey("summon_enabled")) {
            summonEnabled = (Boolean) config.specialAbilities.get("summon_enabled");
        }

        // 启用DNA螺旋魔法攻击技能
        boolean magicAttackEnabled = true;
        if (config.specialAbilities.containsKey("magic_attack_enabled")) {
            magicAttackEnabled = (Boolean) config.specialAbilities.get("magic_attack_enabled");
        }

        // 启用圈式内收缩尖牙攻击技能
        boolean fangCircleEnabled = true;
        if (config.specialAbilities.containsKey("fang_circle_enabled")) {
            fangCircleEnabled = (Boolean) config.specialAbilities.get("fang_circle_enabled");
        }

        // 启动综合技能系统
        startDisasterSummonerAllSkills(evoker, config, circleEnabled, summonEnabled, magicAttackEnabled, fangCircleEnabled);

        if (debugMode) {
            logger.info("为灾厄唤魔者启用技能 - 脚底粒子圆圈: " + circleEnabled +
                       ", 召唤卫道士: " + summonEnabled +
                       ", DNA螺旋魔法攻击: " + magicAttackEnabled +
                       ", 圈式内收缩尖牙攻击: " + fangCircleEnabled);
        }
    }

    /**
     * 启动灾厄唤魔者的综合技能系统（四大技能）
     */
    private void startDisasterSummonerAllSkills(org.bukkit.entity.Evoker evoker, EntityOverrideConfig config,
                                                boolean circleEnabled, boolean summonEnabled,
                                                boolean magicAttackEnabled, boolean fangCircleEnabled) {

        // 从配置中获取各种参数
        final double circleRadius = config.specialAbilities.containsKey("circle_radius") ?
            ((Number) config.specialAbilities.get("circle_radius")).doubleValue() : 2.0;

        final int summonInterval = config.specialAbilities.containsKey("summon_interval") ?
            (Integer) config.specialAbilities.get("summon_interval") : 10000; // 10秒
        final int maxMinions = config.specialAbilities.containsKey("max_minions") ?
            (Integer) config.specialAbilities.get("max_minions") : 10;

        final int magicAttackInterval = config.specialAbilities.containsKey("magic_attack_interval") ?
            (Integer) config.specialAbilities.get("magic_attack_interval") : 2000; // 2秒

        final int fangCircleInterval = config.specialAbilities.containsKey("fang_circle_interval") ?
            (Integer) config.specialAbilities.get("fang_circle_interval") : 8000; // 8秒

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;
            private long lastSummonTime = System.currentTimeMillis();
            private long lastMagicAttackTime = System.currentTimeMillis();
            private long lastFangCircleTime = System.currentTimeMillis();

            @Override
            public void run() {
                if (evoker == null || evoker.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(evoker);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                Location location = evoker.getLocation();

                // 1. 脚底粒子圆圈（每0.5秒）
                if (circleEnabled && tickCounter % 10 == 0) {
                    performSummonerCircleEffect(evoker, location, circleRadius);
                }

                // 2. 召唤卫道士（每10秒）
                if (summonEnabled && currentTime - lastSummonTime >= summonInterval) {
                    performSummonerSummonMinions(evoker, maxMinions);
                    lastSummonTime = currentTime;
                }

                // 3. DNA螺旋魔法攻击（每2秒）
                if (magicAttackEnabled && currentTime - lastMagicAttackTime >= magicAttackInterval) {
                    performDNAMagicAttack(evoker);
                    lastMagicAttackTime = currentTime;
                }

                // 4. 圈式内收缩尖牙攻击（每8秒）
                if (fangCircleEnabled && currentTime - lastFangCircleTime >= fangCircleInterval) {
                    performFangCircleAttack(evoker);
                    lastFangCircleTime = currentTime;
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(evoker, skillTask);

        if (debugMode) {
            logger.info("为灾厄唤魔者启动综合技能系统");
        }
    }

    /**
     * 执行唤魔者脚底粒子圆圈效果
     */
    private void performSummonerCircleEffect(org.bukkit.entity.Evoker evoker, Location location, double radius) {
        // 创建脚底龙息粒子圆圈
        for (int i = 0; i < 32; i++) {
            double angle = 2 * Math.PI * i / 32;
            double x = location.getX() + radius * Math.cos(angle);
            double z = location.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(location.getWorld(), x, location.getY(), z);

            // 生成龙息粒子
            location.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 执行唤魔者召唤卫道士仆从
     */
    private void performSummonerSummonMinions(org.bukkit.entity.Evoker evoker, int maxMinions) {
        Location location = evoker.getLocation();

        // 检查周围已有的卫道士数量
        int nearbyVindicators = 0;
        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 20, 20, 20)) {
            if (entity instanceof org.bukkit.entity.Vindicator && entity.hasMetadata("summonerMinion")) {
                nearbyVindicators++;
            }
        }

        // 如果卫道士数量少于最大值，召唤新的
        if (nearbyVindicators < maxMinions) {
            int toSummon = Math.min(2, maxMinions - nearbyVindicators); // 每次最多召唤2个

            for (int i = 0; i < toSummon; i++) {
                // 在周围随机位置召唤卫道士
                double offsetX = (Math.random() - 0.5) * 6;
                double offsetZ = (Math.random() - 0.5) * 6;
                Location summonLoc = location.clone().add(offsetX, 0, offsetZ);

                // 生成卫道士
                org.bukkit.entity.Vindicator minion = (org.bukkit.entity.Vindicator)
                    location.getWorld().spawnEntity(summonLoc, org.bukkit.entity.EntityType.VINDICATOR);

                // 设置卫道士属性
                minion.setCustomName("§5唤魔者仆从");
                minion.setCustomNameVisible(true);

                // 添加速度效果
                minion.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II

                // 添加元数据标记为唤魔者仆从
                minion.setMetadata("summonerMinion", new FixedMetadataValue(plugin, true));
                minion.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            }

            // 播放召唤效果
            location.getWorld().spawnParticle(org.bukkit.Particle.WITCH,
                location.clone().add(0, 1, 0), 20, 1, 1, 1, 0.1);

            if (debugMode) {
                logger.info("灾厄唤魔者召唤了 " + toSummon + " 个卫道士仆从，当前总数: " + (nearbyVindicators + toSummon));
            }
        }
    }

    /**
     * 执行DNA螺旋魔法攻击
     */
    private void performDNAMagicAttack(org.bukkit.entity.Evoker evoker) {
        Location location = evoker.getLocation().add(0, 1.0, 0); // 从唤魔者中心位置发射

        // 寻找最近的玩家
        org.bukkit.entity.Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (org.bukkit.entity.Entity entity : evoker.getNearbyEntities(25, 25, 25)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = location.distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            final org.bukkit.entity.Player finalTarget = target;
            final Location startLoc = location.clone();
            final Location targetLoc = target.getLocation().add(0, 1, 0);

            // 计算方向向量
            org.bukkit.util.Vector direction = targetLoc.toVector().subtract(startLoc.toVector()).normalize();

            // 创建DNA螺旋魔法攻击任务
            new BukkitRunnable() {
                private Location currentLoc = startLoc.clone();
                private int ticks = 0;
                private final int maxTicks = 80; // 最多飞行4秒
                private double spiralAngle = 0;

                @Override
                public void run() {
                    if (evoker == null || evoker.isDead() || ticks >= maxTicks) {
                        this.cancel();
                        return;
                    }

                    // 移动魔法攻击
                    currentLoc.add(direction.clone().multiply(0.4));

                    // 创建DNA螺旋效果
                    spiralAngle += 0.5;
                    for (int i = 0; i < 2; i++) {
                        double angle = spiralAngle + (i * Math.PI);
                        double offsetX = Math.cos(angle) * 0.5;
                        double offsetZ = Math.sin(angle) * 0.5;

                        Location spiralLoc = currentLoc.clone().add(offsetX, 0, offsetZ);

                        // 生成螺旋粒子效果（紫色和白色交替）
                        if (i == 0) {
                            spiralLoc.getWorld().spawnParticle(org.bukkit.Particle.WITCH, spiralLoc, 1, 0, 0, 0, 0);
                        } else {
                            spiralLoc.getWorld().spawnParticle(org.bukkit.Particle.CLOUD, spiralLoc, 1, 0, 0, 0, 0);
                        }
                    }

                    // 检查是否击中玩家
                    for (org.bukkit.entity.Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.5, 1.5, 1.5)) {
                        if (entity instanceof org.bukkit.entity.Player) {
                            org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;

                            // 尖牙攻击效果
                            Location playerLoc = player.getLocation();
                            for (int j = 0; j < 8; j++) {
                                double fangAngle = j * Math.PI / 4;
                                double fangX = playerLoc.getX() + Math.cos(fangAngle) * 2;
                                double fangZ = playerLoc.getZ() + Math.sin(fangAngle) * 2;
                                Location fangLoc = new Location(playerLoc.getWorld(), fangX, playerLoc.getY(), fangZ);

                                // 生成尖牙效果
                                try {
                                    // 尝试使用ENCHANTED_HIT粒子
                                    playerLoc.getWorld().spawnParticle(org.bukkit.Particle.ENCHANTED_HIT, fangLoc, 5, 0.2, 0.5, 0.2, 0);
                                } catch (Exception e) {
                                    // 如果不可用，使用CRIT作为替代
                                    playerLoc.getWorld().spawnParticle(org.bukkit.Particle.CRIT, fangLoc, 5, 0.2, 0.5, 0.2, 0);
                                }
                            }

                            // 造成魔法伤害
                            player.damage(8.0, evoker);

                            // 爆炸效果
                            currentLoc.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, currentLoc, 3, 1, 1, 1, 0);
                            currentLoc.getWorld().playSound(currentLoc, org.bukkit.Sound.ENTITY_EVOKER_CAST_SPELL, 1.0f, 1.0f);

                            if (debugMode) {
                                logger.info("灾厄唤魔者的DNA螺旋魔法攻击击中玩家 " + player.getName() + "，造成 8 点魔法伤害");
                            }

                            this.cancel();
                            return;
                        }
                    }

                    ticks++;
                }
            }.runTaskTimer(plugin, 0, 1);

            if (debugMode) {
                logger.info("灾厄唤魔者向玩家 " + target.getName() + " 发射了DNA螺旋魔法攻击");
            }
        }
    }

    /**
     * 执行圈式内收缩尖牙攻击
     */
    private void performFangCircleAttack(org.bukkit.entity.Evoker evoker) {
        // 寻找最近的玩家
        org.bukkit.entity.Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (org.bukkit.entity.Entity entity : evoker.getNearbyEntities(25, 25, 25)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = evoker.getLocation().distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            final org.bukkit.entity.Player finalTarget = target;
            final Location playerLoc = target.getLocation();

            // 设置唤魔者的法术为尖牙攻击
            evoker.setSpell(org.bukkit.entity.Spellcaster.Spell.FANGS);

            // 播放施法音效
            evoker.getWorld().playSound(evoker.getLocation(), org.bukkit.Sound.ENTITY_EVOKER_PREPARE_ATTACK, 1.0f, 1.0f);

            // 第一阶段：外圈尖牙（16个，半径4格）
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (evoker == null || evoker.isDead() || finalTarget == null || finalTarget.isDead()) {
                        this.cancel();
                        return;
                    }

                    createFangCircle(playerLoc, evoker, 16, 4.0);

                    // 第二阶段：中圈尖牙（12个，半径2.5格）- 0.5秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            if (evoker == null || evoker.isDead() || finalTarget == null || finalTarget.isDead()) {
                                this.cancel();
                                return;
                            }

                            createFangCircle(playerLoc, evoker, 12, 2.5);

                            // 第三阶段：内圈尖牙（8个，半径1格）- 再过0.5秒
                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    if (evoker == null || evoker.isDead() || finalTarget == null || finalTarget.isDead()) {
                                        this.cancel();
                                        return;
                                    }

                                    createFangCircle(playerLoc, evoker, 8, 1.0);

                                    // 第四阶段：中心尖牙+爆炸 - 再过0.25秒
                                    new BukkitRunnable() {
                                        @Override
                                        public void run() {
                                            if (evoker == null || evoker.isDead() || finalTarget == null || finalTarget.isDead()) {
                                                this.cancel();
                                                return;
                                            }

                                            createCenterFang(playerLoc, evoker);
                                        }
                                    }.runTaskLater(plugin, 5); // 0.25秒后
                                }
                            }.runTaskLater(plugin, 10); // 0.5秒后
                        }
                    }.runTaskLater(plugin, 10); // 0.5秒后
                }
            }.runTaskLater(plugin, 15); // 0.75秒后开始

            if (debugMode) {
                logger.info("灾厄唤魔者对玩家 " + target.getName() + " 发动了圈式内收缩尖牙攻击");
            }
        }
    }

    /**
     * 创建尖牙圆圈
     */
    private void createFangCircle(Location center, org.bukkit.entity.Evoker evoker, int count, double radius) {
        for (int i = 0; i < count; i++) {
            double angle = 2 * Math.PI * i / count;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);

            Location fangLoc = center.clone().add(x, 0, z);
            fangLoc.setY(fangLoc.getWorld().getHighestBlockYAt(fangLoc) + 1);

            // 生成尖牙实体
            org.bukkit.entity.Entity fang = fangLoc.getWorld().spawnEntity(fangLoc, org.bukkit.entity.EntityType.EVOKER_FANGS);

            // 设置尖牙的所有者
            if (fang instanceof org.bukkit.entity.EvokerFangs) {
                org.bukkit.entity.EvokerFangs evokerFangs = (org.bukkit.entity.EvokerFangs) fang;
                evokerFangs.setOwner(evoker);
            }

            // 添加龙息粒子效果
            fangLoc.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH,
                fangLoc.clone().add(0, 0.5, 0), 8, 0.2, 0.5, 0.2, 0);
        }

        // 播放音效
        center.getWorld().playSound(center, org.bukkit.Sound.ENTITY_EVOKER_PREPARE_ATTACK, 1.0f, 1.2f);
    }

    /**
     * 创建中心尖牙+爆炸效果
     */
    private void createCenterFang(Location center, org.bukkit.entity.Evoker evoker) {
        Location fangLoc = center.clone();
        fangLoc.setY(fangLoc.getWorld().getHighestBlockYAt(fangLoc) + 1);

        // 生成中心尖牙
        org.bukkit.entity.Entity fang = fangLoc.getWorld().spawnEntity(fangLoc, org.bukkit.entity.EntityType.EVOKER_FANGS);

        // 设置尖牙的所有者
        if (fang instanceof org.bukkit.entity.EvokerFangs) {
            org.bukkit.entity.EvokerFangs evokerFangs = (org.bukkit.entity.EvokerFangs) fang;
            evokerFangs.setOwner(evoker);
        }

        // 创建爆炸效果（不造成方块破坏）
        fangLoc.getWorld().createExplosion(fangLoc, 0.0F, false, false);

        // 添加大量龙息粒子
        fangLoc.getWorld().spawnParticle(org.bukkit.Particle.DRAGON_BREATH,
            fangLoc.clone().add(0, 0.5, 0), 20, 0.5, 0.5, 0.5, 0.1);

        // 播放爆炸音效
        center.getWorld().playSound(center, org.bukkit.Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);
    }

    /**
     * 启用灾厄劫掠兽的技能（脚底暴击圆圈）
     */
    private void enableDisasterRavagerBeastSkills(org.bukkit.entity.Ravager ravager, EntityOverrideConfig config) {
        // 启用脚底暴击圆圈技能
        boolean circleAttackEnabled = true;
        if (config.specialAbilities.containsKey("circle_attack_enabled")) {
            circleAttackEnabled = (Boolean) config.specialAbilities.get("circle_attack_enabled");
        }

        // 启动技能系统
        startDisasterRavagerBeastSkills(ravager, config, circleAttackEnabled);

        if (debugMode) {
            logger.info("为灾厄劫掠兽启用技能 - 脚底暴击圆圈: " + circleAttackEnabled);
        }
    }

    /**
     * 启动灾厄劫掠兽的技能系统（脚底暴击圆圈）
     */
    private void startDisasterRavagerBeastSkills(org.bukkit.entity.Ravager ravager, EntityOverrideConfig config,
                                                 boolean circleAttackEnabled) {

        // 从配置中获取参数
        final double circleRadius = config.specialAbilities.containsKey("circle_radius") ?
            ((Number) config.specialAbilities.get("circle_radius")).doubleValue() : 3.0;
        final double circleDamage = config.specialAbilities.containsKey("circle_damage") ?
            ((Number) config.specialAbilities.get("circle_damage")).doubleValue() : 2.0;
        final double knockbackStrength = config.specialAbilities.containsKey("knockback_strength") ?
            ((Number) config.specialAbilities.get("knockback_strength")).doubleValue() : 8.0;

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                if (ravager == null || ravager.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(ravager);
                    return;
                }

                Location location = ravager.getLocation();

                // 脚底暴击圆圈（每0.5秒）
                if (circleAttackEnabled && tickCounter % 10 == 0) {
                    performRavagerCircleAttack(ravager, location, circleRadius, circleDamage, knockbackStrength);
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(ravager, skillTask);

        if (debugMode) {
            logger.info("为灾厄劫掠兽启动技能系统");
        }
    }

    /**
     * 执行劫掠兽脚底暴击圆圈攻击
     */
    private void performRavagerCircleAttack(org.bukkit.entity.Ravager ravager, Location location,
                                            double radius, double damage, double knockbackStrength) {
        // 创建脚底暴击粒子圆圈
        for (int i = 0; i < 48; i++) {
            double angle = 2 * Math.PI * i / 48;
            double x = location.getX() + radius * Math.cos(angle);
            double z = location.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(location.getWorld(), x, location.getY(), z);

            // 生成暴击粒子
            location.getWorld().spawnParticle(org.bukkit.Particle.CRIT, particleLoc, 1, 0, 0, 0, 0);
        }

        // 检查圆圈范围内的玩家
        for (org.bukkit.entity.Entity entity : ravager.getNearbyEntities(radius, radius, radius)) {
            if (entity instanceof org.bukkit.entity.Player) {
                org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;
                Location playerLoc = player.getLocation();

                // 计算水平距离
                double dx = playerLoc.getX() - location.getX();
                double dz = playerLoc.getZ() - location.getZ();
                double distance = Math.sqrt(dx * dx + dz * dz);

                // 如果玩家在圆圈范围内（水平距离小于等于半径，且高度差不超过2）
                if (distance <= radius && Math.abs(playerLoc.getY() - location.getY()) <= 2.0) {
                    // 造成伤害
                    player.damage(damage, ravager);

                    // 强力击退效果（8格击退）
                    org.bukkit.util.Vector knockback = player.getLocation().subtract(location).toVector().normalize();
                    knockback.multiply(knockbackStrength).setY(1.0); // 强力水平击退 + 向上推力
                    player.setVelocity(knockback);

                    // 额外的击退效果确保达到8格距离
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            if (player != null && !player.isDead()) {
                                // 再次施加击退力，确保达到8格效果
                                org.bukkit.util.Vector extraKnockback = player.getLocation().subtract(location).toVector().normalize();
                                extraKnockback.multiply(knockbackStrength * 0.5).setY(0.3);
                                player.setVelocity(extraKnockback);
                            }
                        }
                    }.runTaskLater(plugin, 3); // 0.15秒后再次击退

                    // 添加缓慢X效果（10秒）
                    player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                        org.bukkit.potion.PotionEffectType.SLOWNESS, 200, 9)); // 缓慢X，10秒

                    // 添加反胃效果（5秒）
                    player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                        org.bukkit.potion.PotionEffectType.NAUSEA, 100, 0)); // 反胃I，5秒

                    // 播放受伤音效
                    player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 0.8f, 0.8f);

                    // 显示受伤粒子
                    player.getWorld().spawnParticle(org.bukkit.Particle.DAMAGE_INDICATOR,
                        player.getLocation().add(0, 1, 0), 8, 0.5, 0.5, 0.5, 0);

                    // 显示击退粒子效果
                    player.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION,
                        player.getLocation(), 3, 0.5, 0.5, 0.5, 0);

                    if (debugMode) {
                        logger.info("灾厄劫掠兽的脚底暴击圆圈对玩家 " + player.getName() +
                                   " 造成了 " + damage + " 点伤害，击退 " + knockbackStrength + " 格");
                    }
                }
            }
        }

        // 播放劫掠兽咆哮音效
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_RAVAGER_ROAR, 0.6f, 1.0f);
    }

    /**
     * 实体攻击事件监听器
     * 处理剧毒攻击等特殊技能
     */
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // 检查攻击者是否是UserCustomEntity生成的实体
        if (!(event.getDamager() instanceof LivingEntity)) {
            return;
        }

        LivingEntity attacker = (LivingEntity) event.getDamager();

        // 检查是否是UserCustomEntity
        if (!attacker.hasMetadata("userCustomEntity")) {
            return;
        }

        // 检查被攻击者是否是玩家
        if (!(event.getEntity() instanceof Player)) {
            return;
        }

        Player victim = (Player) event.getEntity();

        // 处理剧毒攻击
        if (attacker.hasMetadata("poisonAttacker")) {
            handlePoisonAttack(attacker, victim);
        }

        // 处理变异尸壳的混乱物品栏攻击
        if (attacker.hasMetadata("mutantHusk")) {
            handleInventoryShuffleAttack(attacker, victim);
        }
    }

    /**
     * 处理剧毒攻击
     */
    private void handlePoisonAttack(LivingEntity attacker, Player victim) {
        try {
            // 获取剧毒参数
            int poisonLevel = 0;
            int poisonDuration = 60;
            double poisonChance = 0.8;

            if (attacker.hasMetadata("poisonLevel")) {
                poisonLevel = attacker.getMetadata("poisonLevel").get(0).asInt();
            }

            if (attacker.hasMetadata("poisonDuration")) {
                poisonDuration = attacker.getMetadata("poisonDuration").get(0).asInt();
            }

            if (attacker.hasMetadata("poisonChance")) {
                poisonChance = attacker.getMetadata("poisonChance").get(0).asDouble();
            }

            // 概率判断
            if (Math.random() > poisonChance) {
                return;
            }

            // 施加剧毒效果
            PotionEffect poisonEffect = new PotionEffect(PotionEffectType.POISON, poisonDuration, poisonLevel);
            victim.addPotionEffect(poisonEffect);

            if (debugMode) {
                logger.info("变异僵尸对玩家 " + victim.getName() + " 施加了剧毒效果（等级" + (poisonLevel + 1) + "，持续" + (poisonDuration / 20) + "秒）");
            }

        } catch (Exception e) {
            logger.warning("处理剧毒攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 处理变异尸壳的混乱物品栏攻击
     */
    private void handleInventoryShuffleAttack(LivingEntity attacker, Player victim) {
        try {
            // 检查是否启用混乱物品栏功能
            boolean shuffleEnabled = true;
            if (attacker.hasMetadata("shuffleEnabled")) {
                shuffleEnabled = attacker.getMetadata("shuffleEnabled").get(0).asBoolean();
            }

            if (!shuffleEnabled) {
                return;
            }

            // 混乱玩家物品栏
            shufflePlayerHotbar(victim);

            // 播放混乱音效
            victim.getWorld().playSound(victim.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 0.5f, 1.2f);

            // 显示混乱粒子
            victim.getWorld().spawnParticle(Particle.PORTAL, victim.getLocation().add(0, 1, 0), 20, 0.5, 0.5, 0.5, 0.05);

            // 不发送提示信息（避免刷屏）
            // victim.sendMessage("§e你被变异尸壳攻击了！§c物品栏被混乱了！");

            if (debugMode) {
                logger.info("变异尸壳对玩家 " + victim.getName() + " 使用了混乱物品栏攻击");
            }

        } catch (Exception e) {
            logger.warning("处理混乱物品栏攻击时出错: " + e.getMessage());
        }
    }

    /**
     * 混乱玩家的物品栏（1-9格）
     * 与原版CustomZombie的shufflePlayerHotbar方法完全一致
     *
     * @param player 要混乱物品栏的玩家
     */
    private void shufflePlayerHotbar(Player player) {
        // 获取玩家物品栏
        PlayerInventory inventory = player.getInventory();

        // 创建物品列表
        List<ItemStack> hotbarItems = new ArrayList<>();
        for (int i = 0; i < 9; i++) {
            hotbarItems.add(inventory.getItem(i));
        }

        // 打乱物品列表
        Collections.shuffle(hotbarItems);

        // 将打乱后的物品放回物品栏
        for (int i = 0; i < 9; i++) {
            inventory.setItem(i, hotbarItems.get(i));
        }

        // 更新玩家物品栏
        player.updateInventory();
    }

    /**
     * 实体死亡事件监听器
     * 清理粒子效果任务和处理死亡后技能
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();

        // 检查是否是UserCustomEntity生成的实体
        if (entity.hasMetadata("userCustomEntity")) {
            // 减少实体计数器
            currentUserCustomEntityCount = Math.max(0, currentUserCustomEntityCount - 1);

            // 清理粒子效果任务
            cleanupParticleEffectTask(entity);

            // 处理特定实体的死亡后技能
            if (entity.hasMetadata("entityId")) {
                String entityId = entity.getMetadata("entityId").get(0).asString();
                handleEntityDeathSkills(entity, entityId);

                if (debugMode && logEntitySpawn) {
                    logger.info("用户自定义实体死亡: " + entityId + " (剩余数量: " + currentUserCustomEntityCount + "/" + maxUserCustomEntities + ")");
                }
            }

            if (debugMode && logSkillExecution) {
                logger.info("清理已死亡UserCustomEntity的粒子效果任务");
            }
        }
    }

    /**
     * 处理实体死亡后的特殊技能
     */
    private void handleEntityDeathSkills(LivingEntity entity, String entityId) {
        switch (entityId) {
            case "idc4": // 变异爬行者死亡后给附近玩家速度效果
                handleMutantCreeperDeathSkill(entity);
                break;
            // 其他实体的死亡技能可以在这里添加
        }
    }

    /**
     * 处理变异爬行者死亡后的技能（给附近玩家速度效果）
     */
    private void handleMutantCreeperDeathSkill(LivingEntity creeper) {
        try {
            Location deathLocation = creeper.getLocation();

            // 从配置中获取参数
            double effectRange = 10.0; // 默认10格范围
            int speedLevel = 1; // 默认速度2（0-based）
            int speedDuration = 600; // 默认30秒（600tick）

            // 尝试从配置中读取参数
            EntityOverrideConfig config = configCache.get("idc4");
            if (config != null && config.specialAbilities != null) {
                if (config.specialAbilities.containsKey("death_effect_range")) {
                    Object rangeObj = config.specialAbilities.get("death_effect_range");
                    if (rangeObj instanceof Double) {
                        effectRange = (Double) rangeObj;
                    } else if (rangeObj instanceof Integer) {
                        effectRange = ((Integer) rangeObj).doubleValue();
                    }
                }

                if (config.specialAbilities.containsKey("death_speed_level")) {
                    speedLevel = (Integer) config.specialAbilities.get("death_speed_level");
                }

                if (config.specialAbilities.containsKey("death_speed_duration")) {
                    speedDuration = (Integer) config.specialAbilities.get("death_speed_duration");
                }
            }

            // 查找范围内的所有玩家
            for (Entity entity : deathLocation.getWorld().getNearbyEntities(deathLocation, effectRange, effectRange, effectRange)) {
                if (entity instanceof Player) {
                    Player player = (Player) entity;

                    // 给玩家添加速度效果（修复原版的错误时间）
                    PotionEffect speedEffect = new PotionEffect(PotionEffectType.SPEED, speedDuration, speedLevel);
                    player.addPotionEffect(speedEffect);

                    // 发送消息给玩家
                    player.sendMessage("§a变异爬行者死亡释放的能量让你获得了速度提升！");

                    if (debugMode) {
                        logger.info("变异爬行者死亡给玩家 " + player.getName() + " 添加了速度效果（等级" + (speedLevel + 1) + "，持续" + (speedDuration / 20) + "秒）");
                    }
                }
            }

            // 播放死亡特效
            deathLocation.getWorld().spawnParticle(org.bukkit.Particle.EXPLOSION, deathLocation, 10, 1.0, 1.0, 1.0, 0.1);
            deathLocation.getWorld().spawnParticle(org.bukkit.Particle.ELECTRIC_SPARK, deathLocation, 20, 2.0, 2.0, 2.0, 0.1);
            deathLocation.getWorld().playSound(deathLocation, org.bukkit.Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.2f);

        } catch (Exception e) {
            logger.warning("处理变异爬行者死亡技能时出错: " + e.getMessage());
        }
    }

    /**
     * 启用变异僵尸马的技能（撞击伤害、召唤武装僵尸、持续粒子效果、智能追踪移动、飞行能力）
     */
    private void enableMutantZombieHorseSkills(org.bukkit.entity.ZombieHorse zombieHorse, EntityOverrideConfig config) {
        // 启用撞击伤害技能
        boolean collisionDamageEnabled = true;
        if (config.specialAbilities.containsKey("collision_damage_enabled")) {
            collisionDamageEnabled = (Boolean) config.specialAbilities.get("collision_damage_enabled");
        }

        // 启用召唤武装僵尸技能
        boolean summonZombiesEnabled = true;
        if (config.specialAbilities.containsKey("summon_zombies_enabled")) {
            summonZombiesEnabled = (Boolean) config.specialAbilities.get("summon_zombies_enabled");
        }

        // 启用持续粒子效果
        boolean particleEnabled = true;
        if (config.specialAbilities.containsKey("particle_enabled")) {
            particleEnabled = (Boolean) config.specialAbilities.get("particle_enabled");
        }

        // 启用智能追踪移动
        boolean smartMovementEnabled = true;
        if (config.specialAbilities.containsKey("smart_movement_enabled")) {
            smartMovementEnabled = (Boolean) config.specialAbilities.get("smart_movement_enabled");
        }

        // 启用飞行能力
        boolean flyingEnabled = true;
        if (config.specialAbilities.containsKey("flying_enabled")) {
            flyingEnabled = (Boolean) config.specialAbilities.get("flying_enabled");
        }

        // 启用视线跟踪
        boolean lookAtPlayerEnabled = true;
        if (config.specialAbilities.containsKey("look_at_player_enabled")) {
            lookAtPlayerEnabled = (Boolean) config.specialAbilities.get("look_at_player_enabled");
        }

        // 启动综合技能系统
        startMutantZombieHorseAllSkills(zombieHorse, config, collisionDamageEnabled, summonZombiesEnabled,
                                        particleEnabled, smartMovementEnabled, flyingEnabled, lookAtPlayerEnabled);

        if (debugMode) {
            logger.info("为变异僵尸马启用技能 - 撞击伤害: " + collisionDamageEnabled +
                       ", 召唤武装僵尸: " + summonZombiesEnabled +
                       ", 持续粒子效果: " + particleEnabled +
                       ", 智能追踪移动: " + smartMovementEnabled +
                       ", 飞行能力: " + flyingEnabled +
                       ", 视线跟踪: " + lookAtPlayerEnabled);
        }
    }

    /**
     * 启动变异僵尸马的综合技能系统（六大技能）
     */
    private void startMutantZombieHorseAllSkills(org.bukkit.entity.ZombieHorse zombieHorse, EntityOverrideConfig config,
                                                 boolean collisionDamageEnabled, boolean summonZombiesEnabled,
                                                 boolean particleEnabled, boolean smartMovementEnabled,
                                                 boolean flyingEnabled, boolean lookAtPlayerEnabled) {

        // 从配置中获取各种参数
        final double collisionDamage = config.specialAbilities.containsKey("collision_damage") ?
            ((Number) config.specialAbilities.get("collision_damage")).doubleValue() : 8.0;
        final double knockbackStrength = config.specialAbilities.containsKey("knockback_strength") ?
            ((Number) config.specialAbilities.get("knockback_strength")).doubleValue() : 1.5;

        final int summonInterval = config.specialAbilities.containsKey("summon_interval") ?
            (Integer) config.specialAbilities.get("summon_interval") : 10000; // 10秒
        final int maxZombies = config.specialAbilities.containsKey("max_zombies") ?
            (Integer) config.specialAbilities.get("max_zombies") : 4;

        final double trackingRange = config.specialAbilities.containsKey("tracking_range") ?
            ((Number) config.specialAbilities.get("tracking_range")).doubleValue() : 30.0;
        final double flyingRange = config.specialAbilities.containsKey("flying_range") ?
            ((Number) config.specialAbilities.get("flying_range")).doubleValue() : 15.0;
        final double maxTurnSpeed = config.specialAbilities.containsKey("max_turn_speed") ?
            ((Number) config.specialAbilities.get("max_turn_speed")).doubleValue() : 30.0;

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;
            private long lastSummonTime = System.currentTimeMillis();

            @Override
            public void run() {
                if (zombieHorse == null || zombieHorse.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(zombieHorse);
                    return;
                }

                long currentTime = System.currentTimeMillis();
                Location location = zombieHorse.getLocation();

                // 1. 撞击伤害检测（每0.5秒）
                if (collisionDamageEnabled && tickCounter % 10 == 0) {
                    performZombieHorseCollisionDamage(zombieHorse, location, collisionDamage, knockbackStrength);
                }

                // 2. 召唤武装僵尸（每10秒）
                if (summonZombiesEnabled && currentTime - lastSummonTime >= summonInterval) {
                    performSummonArmedZombies(zombieHorse, maxZombies);
                    lastSummonTime = currentTime;
                }

                // 3. 持续粒子效果（每0.25秒）
                if (particleEnabled && tickCounter % 5 == 0) {
                    performZombieHorseParticleEffects(zombieHorse, location);
                }

                // 4. 智能追踪移动（每0.25秒）
                if (smartMovementEnabled && tickCounter % 5 == 0) {
                    performSmartTracking(zombieHorse, trackingRange);
                }

                // 4.5. 视线跟踪（每tick，确保马始终面向玩家）
                if (lookAtPlayerEnabled) {
                    performLookAtPlayer(zombieHorse, trackingRange, maxTurnSpeed);
                }

                // 5. 飞行能力（每2秒）
                if (flyingEnabled && tickCounter % 40 == 0) {
                    performZombieHorseFlying(zombieHorse, flyingRange);
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(zombieHorse, skillTask);

        if (debugMode) {
            logger.info("为变异僵尸马启动综合技能系统");
        }
    }

    /**
     * 执行僵尸马撞击伤害
     */
    private void performZombieHorseCollisionDamage(org.bukkit.entity.ZombieHorse zombieHorse, Location location,
                                                   double damage, double knockbackStrength) {
        // 检查1.5格范围内的玩家
        for (org.bukkit.entity.Entity entity : zombieHorse.getNearbyEntities(1.5, 1.5, 1.5)) {
            if (entity instanceof org.bukkit.entity.Player) {
                org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;

                // 造成撞击伤害
                player.damage(damage, zombieHorse);

                // 击退效果
                org.bukkit.util.Vector knockback = player.getLocation().subtract(location).toVector().normalize();
                knockback.multiply(knockbackStrength).setY(0.3);
                player.setVelocity(knockback);

                // 添加缓慢效果
                player.addPotionEffect(new org.bukkit.potion.PotionEffect(
                    org.bukkit.potion.PotionEffectType.SLOWNESS, 60, 1)); // 缓慢II，3秒

                // 撞击粒子效果
                player.getWorld().spawnParticle(org.bukkit.Particle.CRIT,
                    player.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0);

                // 播放撞击音效
                player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT, 0.8f, 1.0f);

                if (debugMode) {
                    logger.info("变异僵尸马撞击了玩家 " + player.getName() + "，造成 " + damage + " 点伤害");
                }
            }
        }
    }

    /**
     * 执行召唤武装僵尸
     */
    private void performSummonArmedZombies(org.bukkit.entity.ZombieHorse zombieHorse, int maxZombies) {
        Location location = zombieHorse.getLocation();

        // 检查周围已有的武装僵尸数量（检查CustomZombie的id8僵尸）
        int nearbyZombies = 0;
        for (org.bukkit.entity.Entity entity : location.getWorld().getNearbyEntities(location, 20, 20, 20)) {
            if (entity instanceof org.bukkit.entity.Zombie &&
                (entity.hasMetadata("armedZombieMinion") ||
                 (entity.hasMetadata("zombieId") && "id8".equals(entity.getMetadata("zombieId").get(0).asString())))) {
                nearbyZombies++;
            }
        }

        // 如果僵尸数量少于最大值，召唤新的
        if (nearbyZombies < maxZombies) {
            int toSummon = Math.min(4, maxZombies - nearbyZombies); // 每次最多召唤4个

            for (int i = 0; i < toSummon; i++) {
                // 在周围随机位置召唤武装僵尸
                double angle = Math.random() * 2 * Math.PI;
                double distance = 2 + Math.random() * 2; // 2-4格之间
                double x = distance * Math.cos(angle);
                double z = distance * Math.sin(angle);
                Location summonLoc = location.clone().add(x, 0, z);

                // 确保生成位置合适
                summonLoc.setY(location.getWorld().getHighestBlockYAt(summonLoc) + 1);

                try {
                    // 使用CustomZombie的方法召唤id8武装僵尸
                    org.bukkit.entity.Zombie armedZombie = summonCustomZombieId8(summonLoc);

                    if (armedZombie != null) {
                        // 添加元数据标记
                        armedZombie.setMetadata("armedZombieMinion", new FixedMetadataValue(plugin, true));
                        armedZombie.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

                        // 如果僵尸马有游戏会话标记，复制到新僵尸
                        if (zombieHorse.hasMetadata("gameSession")) {
                            String gameName = zombieHorse.getMetadata("gameSession").get(0).asString();
                            armedZombie.setMetadata("gameSession", new FixedMetadataValue(plugin, gameName));
                        }

                        // 显示生成效果
                        summonLoc.getWorld().spawnParticle(org.bukkit.Particle.SOUL_FIRE_FLAME,
                            summonLoc.clone().add(0, 1, 0), 15, 0.3, 0.5, 0.3, 0.05);
                    }
                } catch (Exception e) {
                    logger.warning("召唤CustomZombie id8武装僵尸时出错: " + e.getMessage());
                }
            }

            // 播放召唤效果
            location.getWorld().spawnParticle(org.bukkit.Particle.SOUL,
                location.clone().add(0, 1.5, 0), 30, 1.0, 1.0, 1.0, 0.05);
            location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_ZOMBIE_HORSE_AMBIENT, 1.5f, 0.5f);
            location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_ZOMBIE_AMBIENT, 1.0f, 0.8f);

            if (debugMode) {
                logger.info("变异僵尸马召唤了 " + toSummon + " 个CustomZombie id8武装僵尸，当前总数: " + (nearbyZombies + toSummon));
            }
        }
    }

    /**
     * 召唤CustomZombie的id8武装僵尸
     */
    private org.bukkit.entity.Zombie summonCustomZombieId8(Location location) {
        try {
            // 生成僵尸实体
            org.bukkit.entity.Zombie zombie = (org.bukkit.entity.Zombie) location.getWorld().spawnEntity(location, org.bukkit.entity.EntityType.ZOMBIE);

            // 设置id8武装僵尸的属性（复制CustomZombie的实现）
            zombie.setCustomName("§8武装僵尸");
            zombie.setCustomNameVisible(true);
            zombie.setMaxHealth(100.0);
            zombie.setHealth(100.0);

            // 设置装备（钻石剑 + 全套铁甲）
            zombie.getEquipment().setItemInMainHand(new ItemStack(Material.DIAMOND_SWORD));
            zombie.getEquipment().setHelmet(new ItemStack(Material.IRON_HELMET));
            zombie.getEquipment().setChestplate(new ItemStack(Material.IRON_CHESTPLATE));
            zombie.getEquipment().setLeggings(new ItemStack(Material.IRON_LEGGINGS));
            zombie.getEquipment().setBoots(new ItemStack(Material.IRON_BOOTS));

            // 设置装备掉落概率
            zombie.getEquipment().setItemInMainHandDropChance(0.1F);
            zombie.getEquipment().setHelmetDropChance(0.05F);
            zombie.getEquipment().setChestplateDropChance(0.05F);
            zombie.getEquipment().setLeggingsDropChance(0.05F);
            zombie.getEquipment().setBootsDropChance(0.05F);

            // 添加CustomZombie标记
            zombie.setMetadata("zombieId", new FixedMetadataValue(plugin, "id8"));
            zombie.setMetadata("customZombie", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功召唤CustomZombie id8武装僵尸");
            }

            return zombie;
        } catch (Exception e) {
            logger.warning("召唤CustomZombie id8武装僵尸时出错: " + e.getMessage());
            return null;
        }
    }



    /**
     * 执行僵尸马持续粒子效果
     */
    private void performZombieHorseParticleEffects(org.bukkit.entity.ZombieHorse zombieHorse, Location location) {
        // 在僵尸马周围生成灵魂粒子
        for (int i = 0; i < 3; i++) {
            double offsetX = (Math.random() - 0.5) * 2;
            double offsetY = (Math.random() - 0.5) + 1.0; // 偏向上方
            double offsetZ = (Math.random() - 0.5) * 2;

            Location particleLoc = location.clone().add(offsetX, offsetY, offsetZ);
            zombieHorse.getWorld().spawnParticle(org.bukkit.Particle.SOUL, particleLoc, 1, 0, 0, 0, 0);
        }
    }

    /**
     * 执行智能追踪移动
     */
    private void performSmartTracking(org.bukkit.entity.ZombieHorse zombieHorse, double trackingRange) {
        // 寻找最近的玩家
        org.bukkit.entity.Player target = null;
        double minDistance = trackingRange + 1;

        for (org.bukkit.entity.Entity entity : zombieHorse.getNearbyEntities(trackingRange, trackingRange, trackingRange)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = zombieHorse.getLocation().distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            try {
                // 设置目标玩家，让AI自动处理移动
                if (zombieHorse instanceof org.bukkit.entity.Creature) {
                    ((org.bukkit.entity.Creature) zombieHorse).setTarget(target);
                }

                // 只在距离较远时给予额外推力，避免干扰视线跟踪
                if (minDistance > 8) {
                    Location horseLoc = zombieHorse.getLocation();
                    Location targetLoc = target.getLocation();

                    // 计算移动方向（只考虑水平方向，避免影响朝向）
                    org.bukkit.util.Vector direction = targetLoc.toVector().subtract(horseLoc.toVector());
                    direction.setY(0); // 忽略Y轴差异
                    direction = direction.normalize();

                    // 应用较小的推力，主要依靠AI移动
                    direction.multiply(0.2);
                    direction.setY(0.1); // 轻微向上保持飞行

                    zombieHorse.setVelocity(direction);
                }

                if (debugMode && Math.random() < 0.05) { // 5%概率输出日志，减少刷屏
                    logger.info("变异僵尸马正在追踪玩家 " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
                }
            } catch (Exception e) {
                logger.warning("设置僵尸马追踪目标时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 执行视线跟踪 - 让僵尸马始终面向最近的玩家
     */
    private void performLookAtPlayer(org.bukkit.entity.ZombieHorse zombieHorse, double trackingRange, double maxTurnSpeed) {
        // 寻找最近的玩家
        org.bukkit.entity.Player target = null;
        double minDistance = trackingRange + 1;

        for (org.bukkit.entity.Entity entity : zombieHorse.getNearbyEntities(trackingRange, trackingRange, trackingRange)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = zombieHorse.getLocation().distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            Location horseLoc = zombieHorse.getLocation();
            Location targetLoc = target.getLocation();

            // 计算马应该面向的方向
            double dx = targetLoc.getX() - horseLoc.getX();
            double dz = targetLoc.getZ() - horseLoc.getZ();

            // 计算偏航角（水平方向）
            float yaw = (float) Math.toDegrees(Math.atan2(-dx, dz));

            // 计算俯仰角（垂直方向）
            double dy = targetLoc.getY() - horseLoc.getY();
            double horizontalDistance = Math.sqrt(dx * dx + dz * dz);
            float pitch = (float) -Math.toDegrees(Math.atan2(dy, horizontalDistance));

            // 限制俯仰角范围
            pitch = Math.max(-90, Math.min(90, pitch));

            // 设置马的朝向
            Location newLoc = horseLoc.clone();
            newLoc.setYaw(yaw);
            newLoc.setPitch(pitch);

            // 平滑转向，避免突然转向
            Location currentLoc = zombieHorse.getLocation();
            float currentYaw = currentLoc.getYaw();
            float currentPitch = currentLoc.getPitch();

            // 计算角度差
            float yawDiff = normalizeAngle(yaw - currentYaw);
            float pitchDiff = pitch - currentPitch;

            // 平滑转向（使用配置的转向速度）
            float turnSpeed = (float) maxTurnSpeed;
            float newYaw = currentYaw + Math.max(-turnSpeed, Math.min(turnSpeed, yawDiff));
            float newPitch = currentPitch + Math.max(-turnSpeed, Math.min(turnSpeed, pitchDiff));

            // 应用新的朝向
            newLoc.setYaw(newYaw);
            newLoc.setPitch(newPitch);
            zombieHorse.teleport(newLoc);
        }
    }

    /**
     * 标准化角度到-180到180度范围
     */
    private float normalizeAngle(float angle) {
        while (angle > 180) angle -= 360;
        while (angle < -180) angle += 360;
        return angle;
    }

    /**
     * 执行僵尸马飞行能力
     */
    private void performZombieHorseFlying(org.bukkit.entity.ZombieHorse zombieHorse, double flyingRange) {
        // 寻找最近的玩家
        org.bukkit.entity.Player target = null;
        double minDistance = flyingRange + 1;

        for (org.bukkit.entity.Entity entity : zombieHorse.getNearbyEntities(flyingRange, flyingRange, flyingRange)) {
            if (entity instanceof org.bukkit.entity.Player) {
                double distance = zombieHorse.getLocation().distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    target = (org.bukkit.entity.Player) entity;
                }
            }
        }

        if (target != null) {
            Location horseLoc = zombieHorse.getLocation();
            Location targetLoc = target.getLocation().add(0, 2, 0); // 飞向玩家上方

            // 计算飞行方向
            org.bukkit.util.Vector flyDirection = targetLoc.toVector().subtract(horseLoc.toVector()).normalize();

            // 强力飞行推进
            flyDirection.multiply(1.5); // 飞行速度
            flyDirection.setY(Math.max(flyDirection.getY(), 0.5)); // 确保向上飞行

            // 应用飞行
            zombieHorse.setVelocity(flyDirection);

            // 飞行粒子效果
            zombieHorse.getWorld().spawnParticle(org.bukkit.Particle.CLOUD,
                horseLoc.clone().add(0, -0.5, 0), 5, 0.3, 0.1, 0.3, 0.05);

            if (debugMode) {
                logger.info("变异僵尸马飞向玩家 " + target.getName());
            }
        }
    }

    /**
     * 启用变异岩浆怪的技能（火焰粒子环）
     */
    private void enableMutantMagmaCubeSkills(org.bukkit.entity.MagmaCube magmaCube, EntityOverrideConfig config) {
        // 启用火焰粒子环技能
        boolean flameRingEnabled = true;
        if (config.specialAbilities.containsKey("flame_ring_enabled")) {
            flameRingEnabled = (Boolean) config.specialAbilities.get("flame_ring_enabled");
        }

        // 启动技能系统
        startMutantMagmaCubeSkills(magmaCube, config, flameRingEnabled);

        if (debugMode) {
            logger.info("为变异岩浆怪启用技能 - 火焰粒子环: " + flameRingEnabled);
        }
    }

    /**
     * 启动变异岩浆怪的技能系统（火焰粒子环）
     */
    private void startMutantMagmaCubeSkills(org.bukkit.entity.MagmaCube magmaCube, EntityOverrideConfig config,
                                            boolean flameRingEnabled) {

        // 从配置中获取参数
        final double ringRadius = config.specialAbilities.containsKey("ring_radius") ?
            ((Number) config.specialAbilities.get("ring_radius")).doubleValue() : 2.5;
        final double ringDamage = config.specialAbilities.containsKey("ring_damage") ?
            ((Number) config.specialAbilities.get("ring_damage")).doubleValue() : 2.0;
        final int fireDuration = config.specialAbilities.containsKey("fire_duration") ?
            (Integer) config.specialAbilities.get("fire_duration") : 60; // 3秒

        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                if (magmaCube == null || magmaCube.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(magmaCube);
                    return;
                }

                Location location = magmaCube.getLocation();

                // 火焰粒子环（每0.5秒）
                if (flameRingEnabled && tickCounter % 10 == 0) {
                    performMagmaCubeFlameRing(magmaCube, location, ringRadius, ringDamage, fireDuration);
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(magmaCube, skillTask);

        if (debugMode) {
            logger.info("为变异岩浆怪启动技能系统");
        }
    }

    /**
     * 执行岩浆怪火焰粒子环攻击
     */
    private void performMagmaCubeFlameRing(org.bukkit.entity.MagmaCube magmaCube, Location location,
                                           double radius, double damage, int fireDuration) {
        // 创建火焰粒子环
        for (int i = 0; i < 32; i++) {
            double angle = 2 * Math.PI * i / 32;
            double x = location.getX() + radius * Math.cos(angle);
            double z = location.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(location.getWorld(), x, location.getY(), z);

            // 生成火焰粒子
            location.getWorld().spawnParticle(org.bukkit.Particle.FLAME, particleLoc, 2, 0.1, 0.1, 0.1, 0.02);
        }

        // 检查圆圈范围内的玩家
        for (org.bukkit.entity.Entity entity : magmaCube.getNearbyEntities(radius, radius, radius)) {
            if (entity instanceof org.bukkit.entity.Player) {
                org.bukkit.entity.Player player = (org.bukkit.entity.Player) entity;
                Location playerLoc = player.getLocation();

                // 计算水平距离
                double dx = playerLoc.getX() - location.getX();
                double dz = playerLoc.getZ() - location.getZ();
                double distance = Math.sqrt(dx * dx + dz * dz);

                // 如果玩家在圆圈范围内（水平距离小于等于半径，且高度差不超过2）
                if (distance <= radius && Math.abs(playerLoc.getY() - location.getY()) <= 2.0) {
                    // 造成火焰伤害
                    player.damage(damage, magmaCube);

                    // 着火效果
                    player.setFireTicks(fireDuration);

                    // 火焰粒子效果
                    player.getWorld().spawnParticle(org.bukkit.Particle.FLAME,
                        player.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                    // 播放着火音效
                    player.getWorld().playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_HURT_ON_FIRE, 0.8f, 1.0f);

                    if (debugMode) {
                        logger.info("变异岩浆怪的火焰粒子环对玩家 " + player.getName() +
                                   " 造成了 " + damage + " 点伤害，着火 " + (fireDuration/20) + " 秒");
                    }
                }
            }
        }

        // 播放岩浆怪跳跃音效
        location.getWorld().playSound(location, org.bukkit.Sound.ENTITY_MAGMA_CUBE_JUMP, 0.6f, 0.8f);
    }

    /**
     * 生成变异尸壳 (idc12)
     * 尸壳形态，100血量，速度II+跳跃提升II，双重技能系统
     */
    private LivingEntity spawnMutantHusk(Location location, EntityOverrideConfig config) {
        try {
            // 确定实体类型
            EntityType entityType = config.getEntityType();
            if (entityType == null) {
                entityType = EntityType.HUSK; // 默认为尸壳
            }

            // 生成实体
            org.bukkit.entity.Husk husk = (org.bukkit.entity.Husk) location.getWorld().spawnEntity(location, entityType);

            // 设置基础属性（生命值、名称等）
            if (config.healthOverride > 0) {
                husk.setMaxHealth(config.healthOverride);
                husk.setHealth(config.healthOverride);
            } else {
                husk.setMaxHealth(100.0);
                husk.setHealth(100.0);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                husk.setCustomName(config.customNameOverride);
                husk.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            } else {
                husk.setCustomName("§7§l变异尸壳");
                husk.setCustomNameVisible(false);
            }

            // 设置装备
            setupEntityEquipment(husk, config);

            // 设置默认装备（如果配置中没有指定）
            if (husk.getEquipment().getItemInMainHand().getType() == Material.AIR) {
                // 创建锋利1铁剑
                ItemStack ironSword = new ItemStack(Material.IRON_SWORD);
                ItemMeta swordMeta = ironSword.getItemMeta();
                swordMeta.addEnchant(Enchantment.SHARPNESS, 1, true);
                ironSword.setItemMeta(swordMeta);
                husk.getEquipment().setItemInMainHand(ironSword);
                husk.getEquipment().setItemInMainHandDropChance(0.1F);
            }

            if (husk.getEquipment().getChestplate() == null || husk.getEquipment().getChestplate().getType() == Material.AIR) {
                // 创建保护10粉色皮革胸甲
                ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
                LeatherArmorMeta chestplateMeta = (LeatherArmorMeta) chestplate.getItemMeta();
                chestplateMeta.setColor(Color.FUCHSIA); // 粉色
                chestplateMeta.addEnchant(Enchantment.PROTECTION, 10, true);
                chestplate.setItemMeta(chestplateMeta);
                husk.getEquipment().setChestplate(chestplate);
                husk.getEquipment().setChestplateDropChance(0.05F);
            }

            // 添加默认药水效果（如果配置中没有指定）
            if (config.potionEffects.isEmpty()) {
                husk.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
                husk.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 1)); // 跳跃提升II
            }

            // 启用变异尸壳的技能
            enableMutantHuskSkills(husk, config);

            // 设置元数据标记
            husk.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            husk.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            husk.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            husk.setMetadata("entityId", new FixedMetadataValue(plugin, "idc12"));
            husk.setMetadata("mutantHusk", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成变异尸壳，生命值: " + husk.getHealth() + "/" + husk.getMaxHealth() +
                           "，名称: " + husk.getCustomName());
            }

            return husk;

        } catch (Exception e) {
            logger.severe("生成变异尸壳时出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异尸壳的技能（药水粒子效果 + 召唤强化僵尸 + 混乱物品栏）
     */
    private void enableMutantHuskSkills(org.bukkit.entity.Husk husk, EntityOverrideConfig config) {
        // 启用药水粒子效果
        boolean particleEnabled = true;
        if (config.specialAbilities.containsKey("particle_enabled")) {
            particleEnabled = (Boolean) config.specialAbilities.get("particle_enabled");
        }

        // 启用召唤强化僵尸
        boolean summonEnabled = true;
        if (config.specialAbilities.containsKey("summon_enabled")) {
            summonEnabled = (Boolean) config.specialAbilities.get("summon_enabled");
        }

        // 启用混乱物品栏攻击
        boolean shuffleEnabled = true;
        if (config.specialAbilities.containsKey("shuffle_enabled")) {
            shuffleEnabled = (Boolean) config.specialAbilities.get("shuffle_enabled");
        }

        // 设置混乱物品栏元数据
        if (shuffleEnabled) {
            husk.setMetadata("shuffleEnabled", new FixedMetadataValue(plugin, true));
        }

        if (particleEnabled || summonEnabled) {
            startMutantHuskAllSkills(husk, config, particleEnabled, summonEnabled);
        }

        if (debugMode) {
            logger.info("为变异尸壳启用技能系统 - 粒子效果: " + particleEnabled +
                       ", 召唤僵尸: " + summonEnabled + ", 混乱物品栏: " + shuffleEnabled);
        }
    }

    /**
     * 启动变异尸壳的综合技能系统
     */
    private void startMutantHuskAllSkills(org.bukkit.entity.Husk husk, EntityOverrideConfig config,
                                         final boolean particleEnabled, final boolean summonEnabled) {

        // 从配置中获取技能参数（声明为final以便在内部类中使用）
        final int particleInterval = config.skillCooldownOverrides.getOrDefault("particle_interval", 10); // 默认每0.5秒
        final int summonInterval = config.skillCooldownOverrides.getOrDefault("summon_interval", 160); // 默认每8秒
        final int maxNearbyZombies = config.skillCooldownOverrides.getOrDefault("max_nearby_zombies", 20); // 默认最多20个僵尸
        final int summonCount = config.skillCooldownOverrides.getOrDefault("summon_count", 6); // 默认每次召唤6个

        // 创建综合技能任务
        BukkitTask skillTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                if (husk == null || husk.isDead()) {
                    this.cancel();
                    particleEffectTasks.remove(husk);
                    return;
                }

                // 1. 药水粒子效果（按配置间隔）
                if (particleEnabled && tickCounter % particleInterval == 0) {
                    performHuskParticleEffects(husk);
                }

                // 2. 召唤强化僵尸（按配置间隔）
                if (summonEnabled && tickCounter % summonInterval == 0 && tickCounter > 0) {
                    performSummonStrongZombies(husk, maxNearbyZombies, summonCount);
                }

                tickCounter++;
            }
        }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

        // 保存任务以便后续取消
        particleEffectTasks.put(husk, skillTask);

        if (debugMode) {
            logger.info("为变异尸壳启动综合技能系统");
        }
    }

    /**
     * 执行变异尸壳的药水粒子效果
     */
    private void performHuskParticleEffects(org.bukkit.entity.Husk husk) {
        // 获取尸壳位置
        Location loc = husk.getLocation().add(0, 1, 0);

        // 生成药水效果粒子（传送门粒子）
        husk.getWorld().spawnParticle(Particle.PORTAL,
                loc,
                10, 0.5, 0.5, 0.5, 0.1);
    }

    /**
     * 执行变异尸壳的召唤强化僵尸
     */
    private void performSummonStrongZombies(org.bukkit.entity.Husk husk, int maxNearbyZombies, int summonCount) {
        // 获取尸壳位置
        Location location = husk.getLocation();

        // 检查周围已有的僵尸数量，如果太多就不再召唤
        int nearbyZombies = 0;
        for (Entity entity : location.getWorld().getNearbyEntities(location, 20, 20, 20)) {
            if (entity instanceof Zombie) {
                nearbyZombies++;
            }
        }

        // 如果周围僵尸数量少于限制，才召唤新的僵尸
        if (nearbyZombies < maxNearbyZombies) {
            // 播放召唤效果
            location.getWorld().spawnParticle(Particle.PORTAL,
                    location.clone().add(0, 1, 0), 20, 1, 1, 1, 0.1);
            location.getWorld().playSound(location, Sound.ENTITY_EVOKER_CAST_SPELL, 1.0f, 0.8f);

            // 召唤指定数量的僵尸
            for (int i = 0; i < summonCount; i++) {
                // 计算随机生成位置（在尸壳周围2-4格范围内）
                double angle = Math.random() * 2 * Math.PI;
                double distance = 2 + Math.random() * 2; // 2-4格之间
                double x = distance * Math.cos(angle);
                double z = distance * Math.sin(angle);

                Location spawnLoc = location.clone().add(x, 0, z);

                // 确保生成位置是有效的（在地面上）
                while (!spawnLoc.getBlock().getType().isSolid() && spawnLoc.getY() > 0) {
                    spawnLoc.setY(spawnLoc.getY() - 1);
                }
                spawnLoc.setY(spawnLoc.getY() + 1);

                // 生成僵尸
                Zombie minion = (Zombie) location.getWorld().spawnEntity(spawnLoc, EntityType.ZOMBIE);

                // 设置强化僵尸装备和属性
                setupStrongZombieEquipment(minion);

                // 添加元数据标记为尸壳仆从
                minion.setMetadata("huskMinion", new FixedMetadataValue(plugin, true));
                minion.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));

                // 如果尸壳有游戏会话标记，复制到新僵尸
                if (husk.hasMetadata("gameSession")) {
                    String gameName = husk.getMetadata("gameSession").get(0).asString();
                    minion.setMetadata("gameSession", new FixedMetadataValue(plugin, gameName));
                }
            }

            if (debugMode) {
                logger.info("变异尸壳召唤了 " + summonCount + " 个强化僵尸，当前周围僵尸数量: " + (nearbyZombies + summonCount));
            }
        }
    }

    /**
     * 设置强化僵尸的装备和属性
     */
    private void setupStrongZombieEquipment(Zombie minion) {
        // 设置僵尸属性
        minion.setCustomNameVisible(false); // 不显示名称
        minion.setMaxHealth(20.0);
        minion.setHealth(20.0);

        // 创建锋利1钻石剑
        ItemStack sword = new ItemStack(Material.DIAMOND_SWORD);
        ItemMeta swordMeta = sword.getItemMeta();
        swordMeta.addEnchant(Enchantment.SHARPNESS, 1, true);
        sword.setItemMeta(swordMeta);

        // 创建保护2下界合金套装
        ItemStack helmet = new ItemStack(Material.NETHERITE_HELMET);
        ItemMeta helmetMeta = helmet.getItemMeta();
        helmetMeta.addEnchant(Enchantment.PROTECTION, 2, true);
        helmet.setItemMeta(helmetMeta);

        ItemStack chestplate = new ItemStack(Material.NETHERITE_CHESTPLATE);
        ItemMeta chestplateMeta = chestplate.getItemMeta();
        chestplateMeta.addEnchant(Enchantment.PROTECTION, 2, true);
        chestplate.setItemMeta(chestplateMeta);

        ItemStack leggings = new ItemStack(Material.NETHERITE_LEGGINGS);
        ItemMeta leggingsMeta = leggings.getItemMeta();
        leggingsMeta.addEnchant(Enchantment.PROTECTION, 2, true);
        leggings.setItemMeta(leggingsMeta);

        ItemStack boots = new ItemStack(Material.NETHERITE_BOOTS);
        ItemMeta bootsMeta = boots.getItemMeta();
        bootsMeta.addEnchant(Enchantment.PROTECTION, 2, true);
        boots.setItemMeta(bootsMeta);

        // 设置装备
        minion.getEquipment().setItemInMainHand(sword);
        minion.getEquipment().setHelmet(helmet);
        minion.getEquipment().setChestplate(chestplate);
        minion.getEquipment().setLeggings(leggings);
        minion.getEquipment().setBoots(boots);

        // 设置装备不掉落
        minion.getEquipment().setItemInMainHandDropChance(0.0F);
        minion.getEquipment().setHelmetDropChance(0.0F);
        minion.getEquipment().setChestplateDropChance(0.0F);
        minion.getEquipment().setLeggingsDropChance(0.0F);
        minion.getEquipment().setBootsDropChance(0.0F);

        // 添加速度2效果
        minion.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
    }

    /**
     * 将配置对象转换为附魔Map（处理MemorySection类型转换问题）
     */
    private Map<String, Integer> convertToEnchantmentMap(Object configObject) {
        Map<String, Integer> enchantmentMap = new HashMap<>();

        if (configObject == null) {
            return enchantmentMap;
        }

        if (configObject instanceof org.bukkit.configuration.MemorySection) {
            org.bukkit.configuration.MemorySection section = (org.bukkit.configuration.MemorySection) configObject;
            for (String key : section.getKeys(false)) {
                Object value = section.get(key);
                if (value instanceof Number) {
                    enchantmentMap.put(key, ((Number) value).intValue());
                }
            }
        } else if (configObject instanceof Map) {
            // 如果已经是Map类型，直接转换
            Map<?, ?> map = (Map<?, ?>) configObject;
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                if (entry.getKey() instanceof String && entry.getValue() instanceof Number) {
                    enchantmentMap.put((String) entry.getKey(), ((Number) entry.getValue()).intValue());
                }
            }
        }

        return enchantmentMap;
    }

    /**
     * 根据颜色名称获取Color对象（替代已弃用的Color.valueOf）
     */
    private Color getColorByName(String colorName) {
        switch (colorName.toUpperCase()) {
            case "FUCHSIA":
            case "MAGENTA":
                return Color.FUCHSIA;
            case "RED":
                return Color.RED;
            case "BLUE":
                return Color.BLUE;
            case "GREEN":
                return Color.GREEN;
            case "YELLOW":
                return Color.YELLOW;
            case "ORANGE":
                return Color.ORANGE;
            case "PURPLE":
                return Color.PURPLE;
            case "WHITE":
                return Color.WHITE;
            case "BLACK":
                return Color.BLACK;
            case "GRAY":
            case "GREY":
                return Color.GRAY;
            case "LIME":
                return Color.LIME;
            case "AQUA":
            case "CYAN":
                return Color.AQUA;
            case "NAVY":
                return Color.NAVY;
            case "TEAL":
                return Color.TEAL;
            case "SILVER":
                return Color.SILVER;
            case "MAROON":
                return Color.MAROON;
            case "OLIVE":
                return Color.OLIVE;
            default:
                return null;
        }
    }

    /**
     * 生成IDC13变异僵尸3（流浪者形态）
     */
    private LivingEntity spawnSwampStray(Location location, EntityOverrideConfig config) {
        try {
            // 生成流浪者实体
            org.bukkit.entity.Stray stray = (org.bukkit.entity.Stray) location.getWorld().spawnEntity(location, EntityType.STRAY);

            // 设置基础属性
            if (config.healthOverride > 0) {
                stray.setMaxHealth(config.healthOverride);
                stray.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                stray.setCustomName(config.customNameOverride);
                stray.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度II）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            stray.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC13添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认速度II效果
                stray.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
            }

            // 设置装备
            setupEntityEquipment(stray, config);

            // 启用变异僵尸3的技能
            enableSwampStraySkills(stray, config);

            // 设置元数据标记
            stray.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            stray.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            stray.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            stray.setMetadata("entityId", new FixedMetadataValue(plugin, "idc13"));
            stray.setMetadata("swampStrayMonster", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC13变异僵尸3，生命值: " + stray.getHealth() + "/" + stray.getMaxHealth() +
                           "，名称: " + stray.getCustomName());
            }

            return stray;

        } catch (Exception e) {
            logger.severe("生成IDC13变异僵尸3时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异僵尸3的技能（三连射箭攻击 + 冰霜粒子环）
     */
    private void enableSwampStraySkills(org.bukkit.entity.Stray stray, EntityOverrideConfig config) {
        // 启用三连射箭攻击
        boolean arrowAttackEnabled = true;
        if (config.specialAbilities.containsKey("arrow_attack_enabled")) {
            arrowAttackEnabled = (Boolean) config.specialAbilities.get("arrow_attack_enabled");
        }

        // 启用冰霜粒子环
        boolean frostRingEnabled = true;
        if (config.specialAbilities.containsKey("frost_ring_enabled")) {
            frostRingEnabled = (Boolean) config.specialAbilities.get("frost_ring_enabled");
        }

        if (arrowAttackEnabled || frostRingEnabled) {
            startSwampStrayAllSkills(stray, config, arrowAttackEnabled, frostRingEnabled);
        }

        if (debugMode) {
            logger.info("IDC13变异僵尸3技能启用状态 - 三连射箭: " + arrowAttackEnabled + ", 冰霜粒子环: " + frostRingEnabled);
        }
    }

    /**
     * 启动变异僵尸3的所有技能（三连射箭攻击 + 冰霜粒子环）
     */
    private void startSwampStrayAllSkills(org.bukkit.entity.Stray stray, EntityOverrideConfig config,
                                         final boolean arrowAttackEnabled, final boolean frostRingEnabled) {

        // 从配置中获取技能参数（声明为final以便在内部类中使用）
        final int arrowAttackInterval = config.skillCooldownOverrides.getOrDefault("arrow_attack_interval", 100); // 默认每5秒
        final int frostRingInterval = config.skillCooldownOverrides.getOrDefault("frost_ring_interval", 160); // 默认每8秒
        final int arrowCount = config.skillCooldownOverrides.getOrDefault("arrow_count", 3); // 默认3支箭
        final double attackRange = config.skillCooldownOverrides.getOrDefault("attack_range", 20).doubleValue(); // 默认20格
        final double frostRingRadius = config.skillCooldownOverrides.getOrDefault("frost_ring_radius", 5).doubleValue(); // 默认5格
        final double arrowDamage = config.skillCooldownOverrides.getOrDefault("arrow_damage", 6).doubleValue(); // 默认6点伤害
        final int slownessDuration = config.skillCooldownOverrides.getOrDefault("slowness_duration", 100); // 默认5秒

        BukkitRunnable skillTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                // 检查实体是否还存在
                if (stray == null || stray.isDead() || !stray.isValid()) {
                    this.cancel();
                    // 清理任务记录
                    particleEffectTasks.remove(stray);
                    return;
                }

                tickCounter++;

                // 三连射箭攻击（每arrowAttackInterval tick执行一次）
                if (arrowAttackEnabled && tickCounter % arrowAttackInterval == 0) {
                    performTripleArrowAttack(stray, attackRange, arrowCount, arrowDamage);
                }

                // 冰霜粒子环（每frostRingInterval tick执行一次）
                if (frostRingEnabled && tickCounter % frostRingInterval == 0) {
                    performFrostRingAttack(stray, frostRingRadius, slownessDuration);
                }
            }
        };

        // 启动任务（每tick执行一次）
        BukkitTask task = skillTask.runTaskTimer(plugin, 60, 1); // 3秒后开始，每tick执行

        // 记录任务以便清理
        particleEffectTasks.put(stray, task);

        if (debugMode) {
            logger.info("IDC13变异僵尸3技能任务已启动 - 三连射箭间隔: " + arrowAttackInterval + "tick, 冰霜环间隔: " + frostRingInterval + "tick");
        }
    }

    /**
     * 执行三连射箭攻击
     */
    private void performTripleArrowAttack(org.bukkit.entity.Stray stray, double attackRange, int arrowCount, double arrowDamage) {
        Location location = stray.getLocation();

        // 获取附近的玩家
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    nearbyPlayers.add(player);
                }
            }
        }

        // 如果有附近的玩家，向随机一名玩家射箭
        if (!nearbyPlayers.isEmpty()) {
            // 随机选择一名玩家作为目标
            Player target = nearbyPlayers.get(new Random().nextInt(nearbyPlayers.size()));

            // 播放射箭音效
            location.getWorld().playSound(location, Sound.ENTITY_SKELETON_SHOOT, 1.0f, 0.8f);

            // 计算方向向量
            Vector direction = target.getLocation().add(0, 1, 0).subtract(location).toVector().normalize();

            // 发射多支箭
            for (int i = 0; i < arrowCount; i++) {
                final int index = i;
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (stray == null || stray.isDead() || !stray.isValid()) {
                            return;
                        }

                        // 发射箭矢
                        Arrow arrow = stray.launchProjectile(Arrow.class, direction.multiply(1.2));

                        // 设置箭矢属性
                        arrow.setDamage(arrowDamage); // 设置伤害值
                        arrow.setCritical(true); // 设置为暴击
                        arrow.setGlowing(true); // 设置发光效果

                        // 添加缓慢效果
                        arrow.addCustomEffect(new PotionEffect(PotionEffectType.SLOWNESS, 100, 1), true);

                        // 添加元数据标记
                        arrow.setMetadata("swampStrayArrow", new FixedMetadataValue(plugin, true));

                        // 显示射箭效果
                        location.getWorld().spawnParticle(Particle.SNOWFLAKE, location, 10, 0.5, 0.5, 0.5, 0.1);

                        // 设置箭矢10秒后消失
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (arrow != null && !arrow.isDead()) {
                                    arrow.remove();
                                }
                            }
                        }.runTaskLater(plugin, 200); // 10秒 = 200刻
                    }
                }.runTaskLater(plugin, index * 5); // 每支箭间隔5刻
            }

            if (debugMode) {
                logger.info("IDC13执行三连射箭攻击，目标: " + target.getName() + "，箭矢数量: " + arrowCount);
            }
        }
    }

    /**
     * 执行冰霜粒子环攻击
     */
    private void performFrostRingAttack(org.bukkit.entity.Stray stray, double frostRingRadius, int slownessDuration) {
        Location location = stray.getLocation();

        // 播放冰霜音效
        location.getWorld().playSound(location, Sound.BLOCK_GLASS_BREAK, 1.0f, 1.5f);

        // 创建冰霜粒子环
        for (double angle = 0; angle < Math.PI * 2; angle += Math.PI / 16) {
            double x = frostRingRadius * Math.cos(angle);
            double z = frostRingRadius * Math.sin(angle);
            Location particleLoc = location.clone().add(x, 0.1, z);

            // 显示冰霜粒子
            location.getWorld().spawnParticle(Particle.SNOWFLAKE, particleLoc, 5, 0.2, 0.2, 0.2, 0.0);
            location.getWorld().spawnParticle(Particle.CLOUD, particleLoc, 3, 0.2, 0.2, 0.2, 0.0);
        }

        // 对范围内的玩家施加缓慢效果
        for (Entity entity : location.getWorld().getNearbyEntities(location, frostRingRadius, 3, frostRingRadius)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    // 施加缓慢II效果
                    player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, 1));

                    // 显示受影响效果
                    Location playerLoc = player.getLocation().add(0, 1, 0);
                    location.getWorld().spawnParticle(Particle.SNOWFLAKE, playerLoc, 10, 0.5, 1.0, 0.5, 0.1);

                    if (debugMode) {
                        logger.info("IDC13冰霜粒子环影响玩家: " + player.getName() + "，缓慢效果持续: " + (slownessDuration/20) + "秒");
                    }
                }
            }
        }

        if (debugMode) {
            logger.info("IDC13执行冰霜粒子环攻击，半径: " + frostRingRadius + "格");
        }
    }

    /**
     * 生成IDC14变异僵尸4（凋零骷髅形态）
     */
    private LivingEntity spawnMutantZombie04(Location location, EntityOverrideConfig config) {
        try {
            // 生成凋零骷髅实体
            org.bukkit.entity.WitherSkeleton witherSkeleton = (org.bukkit.entity.WitherSkeleton) location.getWorld().spawnEntity(location, EntityType.WITHER_SKELETON);

            // 设置基础属性
            if (config.healthOverride > 0) {
                witherSkeleton.setMaxHealth(config.healthOverride);
                witherSkeleton.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                witherSkeleton.setCustomName(config.customNameOverride);
                witherSkeleton.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度II）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            witherSkeleton.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC14添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认速度II效果
                witherSkeleton.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
            }

            // 设置装备
            setupEntityEquipment(witherSkeleton, config);

            // 启用变异僵尸4的技能
            enableMutantZombie04Skills(witherSkeleton, config);

            // 设置元数据标记
            witherSkeleton.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            witherSkeleton.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            witherSkeleton.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            witherSkeleton.setMetadata("entityId", new FixedMetadataValue(plugin, "idc14"));
            witherSkeleton.setMetadata("mutantZombie04", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC14变异僵尸4，生命值: " + witherSkeleton.getHealth() + "/" + witherSkeleton.getMaxHealth() +
                           "，名称: " + witherSkeleton.getCustomName());
            }

            return witherSkeleton;

        } catch (Exception e) {
            logger.severe("生成IDC14变异僵尸4时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异僵尸4的技能（机枪射击 + 烟雾粒子环 + 冲刺攻击）
     */
    private void enableMutantZombie04Skills(org.bukkit.entity.WitherSkeleton witherSkeleton, EntityOverrideConfig config) {
        // 启用机枪射击
        boolean machineGunEnabled = true;
        if (config.specialAbilities.containsKey("machine_gun_enabled")) {
            machineGunEnabled = (Boolean) config.specialAbilities.get("machine_gun_enabled");
        }

        // 启用烟雾粒子环
        boolean smokeParticlesEnabled = true;
        if (config.specialAbilities.containsKey("smoke_particles_enabled")) {
            smokeParticlesEnabled = (Boolean) config.specialAbilities.get("smoke_particles_enabled");
        }

        // 启用冲刺攻击
        boolean dashAttackEnabled = true;
        if (config.specialAbilities.containsKey("dash_attack_enabled")) {
            dashAttackEnabled = (Boolean) config.specialAbilities.get("dash_attack_enabled");
        }

        if (machineGunEnabled || smokeParticlesEnabled || dashAttackEnabled) {
            startMutantZombie04AllSkills(witherSkeleton, config, machineGunEnabled, smokeParticlesEnabled, dashAttackEnabled);
        }

        if (debugMode) {
            logger.info("IDC14变异僵尸4技能启用状态 - 机枪射击: " + machineGunEnabled +
                       ", 烟雾粒子: " + smokeParticlesEnabled + ", 冲刺攻击: " + dashAttackEnabled);
        }
    }

    /**
     * 启动变异僵尸4的所有技能（机枪射击 + 烟雾粒子环 + 冲刺攻击）
     */
    private void startMutantZombie04AllSkills(org.bukkit.entity.WitherSkeleton witherSkeleton, EntityOverrideConfig config,
                                             final boolean machineGunEnabled, final boolean smokeParticlesEnabled, final boolean dashAttackEnabled) {

        // 从配置中获取技能参数
        final int machineGunInterval = config.skillCooldownOverrides.getOrDefault("machine_gun_interval", 20); // 默认每1秒
        final int particleInterval = config.skillCooldownOverrides.getOrDefault("particle_interval", 10); // 默认每0.5秒
        final int dashInterval = config.skillCooldownOverrides.getOrDefault("dash_interval", 200); // 默认每10秒
        final double machineGunRange = config.skillCooldownOverrides.getOrDefault("machine_gun_range", 30).doubleValue(); // 默认30格
        final double dashRange = config.skillCooldownOverrides.getOrDefault("dash_range", 15).doubleValue(); // 默认15格
        final int bulletCount = config.skillCooldownOverrides.getOrDefault("bullet_count", 8); // 默认8颗子弹
        final double bulletDamage = config.skillCooldownOverrides.getOrDefault("bullet_damage", 5).doubleValue(); // 默认5点伤害
        final double dashDamage = config.skillCooldownOverrides.getOrDefault("dash_damage", 8).doubleValue(); // 默认8点伤害
        final double spreadAngle = config.skillCooldownOverrides.getOrDefault("spread_angle", 5).doubleValue(); // 默认5度散射

        BukkitRunnable skillTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                // 检查实体是否还存在
                if (witherSkeleton == null || witherSkeleton.isDead() || !witherSkeleton.isValid()) {
                    this.cancel();
                    // 清理任务记录
                    particleEffectTasks.remove(witherSkeleton);
                    return;
                }

                tickCounter++;

                // 机枪射击（每machineGunInterval tick执行一次）
                if (machineGunEnabled && tickCounter % machineGunInterval == 0) {
                    performMachineGunAttack(witherSkeleton, machineGunRange, bulletCount, bulletDamage, spreadAngle);
                }

                // 烟雾粒子环（每particleInterval tick执行一次）
                if (smokeParticlesEnabled && tickCounter % particleInterval == 0) {
                    performSmokeParticleEffect(witherSkeleton);
                }

                // 冲刺攻击（每dashInterval tick执行一次）
                if (dashAttackEnabled && tickCounter % dashInterval == 0) {
                    performDashAttack(witherSkeleton, dashRange, dashDamage);
                }
            }
        };

        // 启动任务（每tick执行一次）
        BukkitTask task = skillTask.runTaskTimer(plugin, 60, 1); // 3秒后开始，每tick执行

        // 记录任务以便清理
        particleEffectTasks.put(witherSkeleton, task);

        if (debugMode) {
            logger.info("IDC14变异僵尸4技能任务已启动 - 机枪间隔: " + machineGunInterval + "tick, 粒子间隔: " + particleInterval + "tick, 冲刺间隔: " + dashInterval + "tick");
        }
    }

    /**
     * 执行机枪射击攻击
     */
    private void performMachineGunAttack(org.bukkit.entity.WitherSkeleton witherSkeleton, double machineGunRange,
                                        int bulletCount, double bulletDamage, double spreadAngle) {
        Location location = witherSkeleton.getLocation();

        // 获取附近的玩家
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, machineGunRange, machineGunRange, machineGunRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    nearbyPlayers.add(player);
                }
            }
        }

        // 如果有附近的玩家，向随机一名玩家射击
        if (!nearbyPlayers.isEmpty()) {
            // 随机选择一名玩家作为目标
            Player target = nearbyPlayers.get(new Random().nextInt(nearbyPlayers.size()));

            // 计算射击方向
            Vector direction = target.getLocation().add(0, 1, 0).subtract(location).toVector().normalize();

            // 播放机枪射击音效
            location.getWorld().playSound(location, Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 2.0f);

            // 模拟机枪射击效果
            simulateMachineGunShot(witherSkeleton, direction, bulletCount, bulletDamage, spreadAngle);

            if (debugMode) {
                logger.info("IDC14执行机枪射击攻击，目标: " + target.getName() + "，子弹数量: " + bulletCount);
            }
        }
    }

    /**
     * 执行烟雾粒子效果
     */
    private void performSmokeParticleEffect(org.bukkit.entity.WitherSkeleton witherSkeleton) {
        Location location = witherSkeleton.getLocation();

        // 在周围生成烟雾粒子
        for (double angle = 0; angle < Math.PI * 2; angle += Math.PI / 8) {
            double x = 2.0 * Math.cos(angle);
            double z = 2.0 * Math.sin(angle);
            Location particleLoc = location.clone().add(x, 0.5, z);

            // 显示烟雾粒子
            location.getWorld().spawnParticle(Particle.LARGE_SMOKE, particleLoc, 3, 0.2, 0.2, 0.2, 0.05);
        }
    }

    /**
     * 执行冲刺攻击
     */
    private void performDashAttack(org.bukkit.entity.WitherSkeleton witherSkeleton, double dashRange, double dashDamage) {
        Location location = witherSkeleton.getLocation();

        // 获取附近的玩家
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, dashRange, dashRange, dashRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    nearbyPlayers.add(player);
                }
            }
        }

        // 如果有附近的玩家，向最近的玩家冲刺
        if (!nearbyPlayers.isEmpty()) {
            // 找到最近的玩家
            Player target = null;
            double minDistance = Double.MAX_VALUE;

            for (Player player : nearbyPlayers) {
                double distance = player.getLocation().distance(location);
                if (distance < minDistance) {
                    minDistance = distance;
                    target = player;
                }
            }

            if (target != null) {
                final Player finalTarget = target;

                // 计算冲刺方向
                Vector direction = finalTarget.getLocation().subtract(location).toVector().normalize();

                // 播放冲刺音效
                location.getWorld().playSound(location, Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 1.5f);

                // 执行冲刺动画
                final int totalSteps = 10; // 冲刺步数
                final double stepDistance = minDistance / totalSteps; // 每步距离

                new BukkitRunnable() {
                    private int steps = 0;

                    @Override
                    public void run() {
                        if (witherSkeleton == null || witherSkeleton.isDead() || !witherSkeleton.isValid() || steps >= totalSteps) {
                            this.cancel();
                            return;
                        }

                        // 移动实体
                        Location newLocation = witherSkeleton.getLocation().add(direction.clone().multiply(stepDistance));
                        witherSkeleton.teleport(newLocation);

                        // 显示冲刺粒子效果
                        newLocation.getWorld().spawnParticle(Particle.LARGE_SMOKE, newLocation, 5, 0.3, 0.3, 0.3, 0.05);

                        // 检查是否接近玩家
                        if (steps == totalSteps - 1) {
                            // 如果玩家在范围内，造成伤害
                            if (finalTarget.getLocation().distance(newLocation) <= 2.0) {
                                // 造成伤害
                                finalTarget.damage(dashDamage, witherSkeleton);

                                // 击退效果
                                finalTarget.setVelocity(direction.clone().multiply(1.0));

                                // 播放伤害音效
                                finalTarget.getWorld().playSound(finalTarget.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);

                                // 显示伤害粒子
                                finalTarget.getWorld().spawnParticle(Particle.CRIT, finalTarget.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);

                                if (debugMode) {
                                    logger.info("IDC14冲刺攻击命中玩家: " + finalTarget.getName() + "，伤害: " + dashDamage);
                                }
                            }
                        }

                        steps++;
                    }
                }.runTaskTimer(plugin, 0, 2); // 立即开始，每2刻执行一次

                if (debugMode) {
                    logger.info("IDC14执行冲刺攻击，目标: " + finalTarget.getName() + "，距离: " + String.format("%.1f", minDistance));
                }
            }
        }
    }

    /**
     * 模拟机枪射击效果
     */
    private void simulateMachineGunShot(org.bukkit.entity.WitherSkeleton shooter, Vector direction,
                                       int bulletCount, double bulletDamage, double spreadAngle) {
        // 获取射击起始位置
        Location shootLocation = shooter.getEyeLocation().clone();

        // 播放机枪射击音效
        shootLocation.getWorld().playSound(shootLocation, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 2.0f);

        // 发射多颗子弹（模拟机枪连射）
        for (int i = 0; i < bulletCount; i++) {
            final int index = i;

            // 延迟发射，模拟连射效果
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (shooter == null || shooter.isDead() || !shooter.isValid()) {
                        return;
                    }

                    // 计算略微偏移的方向（模拟机枪散射）
                    Random random = new Random();
                    double horizontalAngle = random.nextDouble() * spreadAngle - spreadAngle / 2;
                    double verticalAngle = random.nextDouble() * spreadAngle - spreadAngle / 2;

                    // 创建子弹轨迹任务
                    new BukkitRunnable() {
                        Location bulletLoc = shootLocation.clone();
                        Vector bulletDirection = rotateVector(direction.clone(), horizontalAngle, verticalAngle);
                        int distance = 0;
                        final int maxDistance = 50; // 最大射程

                        @Override
                        public void run() {
                            if (distance > maxDistance) {
                                this.cancel();
                                return;
                            }

                            // 移动子弹
                            bulletLoc.add(bulletDirection.multiply(1));
                            distance++;

                            // 显示子弹粒子效果 - 彩色粒子轨迹
                            try {
                                // 主粒子线 - 白色粒子
                                bulletLoc.getWorld().spawnParticle(
                                        Particle.DUST,
                                        bulletLoc,
                                        2,
                                        0, 0, 0,
                                        0,
                                        new Particle.DustOptions(org.bukkit.Color.WHITE, 1.0f)
                                );

                                // 额外粒子线1 - 红色粒子（稍微偏移）
                                Location redLine = bulletLoc.clone().add(
                                        (random.nextDouble() - 0.5) * 0.3,
                                        (random.nextDouble() - 0.5) * 0.3,
                                        (random.nextDouble() - 0.5) * 0.3
                                );
                                bulletLoc.getWorld().spawnParticle(
                                        Particle.DUST,
                                        redLine,
                                        1,
                                        0, 0, 0,
                                        0,
                                        new Particle.DustOptions(org.bukkit.Color.RED, 0.8f)
                                );

                                // 额外粒子线2 - 蓝色粒子（稍微偏移）
                                Location blueLine = bulletLoc.clone().add(
                                        (random.nextDouble() - 0.5) * 0.3,
                                        (random.nextDouble() - 0.5) * 0.3,
                                        (random.nextDouble() - 0.5) * 0.3
                                );
                                bulletLoc.getWorld().spawnParticle(
                                        Particle.DUST,
                                        blueLine,
                                        1,
                                        0, 0, 0,
                                        0,
                                        new Particle.DustOptions(org.bukkit.Color.BLUE, 0.8f)
                                );

                                // 额外粒子线3 - 黄色粒子（稍微偏移）
                                Location yellowLine = bulletLoc.clone().add(
                                        (random.nextDouble() - 0.5) * 0.3,
                                        (random.nextDouble() - 0.5) * 0.3,
                                        (random.nextDouble() - 0.5) * 0.3
                                );
                                bulletLoc.getWorld().spawnParticle(
                                        Particle.DUST,
                                        yellowLine,
                                        1,
                                        0, 0, 0,
                                        0,
                                        new Particle.DustOptions(org.bukkit.Color.YELLOW, 0.8f)
                                );
                            } catch (Exception e) {
                                // 如果DUST粒子不可用，使用备用粒子
                                bulletLoc.getWorld().spawnParticle(Particle.CRIT, bulletLoc, 2, 0.1, 0.1, 0.1, 0.01);
                            }

                            // 检测命中
                            for (Entity entity : bulletLoc.getWorld().getNearbyEntities(bulletLoc, 0.5, 0.5, 0.5)) {
                                if (entity instanceof LivingEntity && entity != shooter) {
                                    LivingEntity hitTarget = (LivingEntity) entity;

                                    // 造成伤害
                                    hitTarget.damage(bulletDamage, shooter);

                                    // 命中特效
                                    bulletLoc.getWorld().spawnParticle(Particle.CRIT, bulletLoc, 15, 0.2, 0.2, 0.2, 0.1);

                                    // 播放命中音效
                                    bulletLoc.getWorld().playSound(bulletLoc, Sound.ENTITY_ARROW_HIT, 1.0f, 1.0f);

                                    // 如果命中玩家，显示击中效果
                                    if (hitTarget instanceof Player) {
                                        Player hitPlayer = (Player) hitTarget;
                                        hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);
                                    }

                                    this.cancel();
                                    return;
                                }
                            }

                            // 检测碰撞
                            Block block = bulletLoc.getBlock();
                            if (block.getType().isSolid()) {
                                // 显示命中方块特效
                                bulletLoc.getWorld().spawnParticle(Particle.CRIT, bulletLoc, 10, 0.1, 0.1, 0.1, 0.05);

                                // 播放命中方块音效
                                bulletLoc.getWorld().playSound(bulletLoc, Sound.BLOCK_STONE_HIT, 0.5f, 1.0f);

                                this.cancel();
                                return;
                            }
                        }
                    }.runTaskTimer(plugin, 0, 1); // 每tick执行一次，实现流畅的子弹轨迹
                }
            }.runTaskLater(plugin, index * 2); // 每颗子弹间隔2刻（0.1秒）
        }
    }

    /**
     * 旋转向量（模拟子弹散射）
     */
    private Vector rotateVector(Vector vector, double horizontalAngle, double verticalAngle) {
        // 简化的向量旋转实现
        double radH = Math.toRadians(horizontalAngle);
        double radV = Math.toRadians(verticalAngle);

        // 水平旋转
        double x = vector.getX() * Math.cos(radH) - vector.getZ() * Math.sin(radH);
        double z = vector.getX() * Math.sin(radH) + vector.getZ() * Math.cos(radH);

        // 垂直旋转
        double y = vector.getY() * Math.cos(radV) - z * Math.sin(radV);
        z = vector.getY() * Math.sin(radV) + z * Math.cos(radV);

        return new Vector(x, y, z).normalize();
    }

    /**
     * 生成IDC15鲜血猪灵（猪灵形态）
     */
    private LivingEntity spawnBloodPiglin(Location location, EntityOverrideConfig config) {
        try {
            // 生成猪灵实体
            org.bukkit.entity.Piglin piglin = (org.bukkit.entity.Piglin) location.getWorld().spawnEntity(location, EntityType.PIGLIN);

            // 设置基础属性
            if (config.healthOverride > 0) {
                piglin.setMaxHealth(config.healthOverride);
                piglin.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                piglin.setCustomName(config.customNameOverride);
                piglin.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置猪灵特殊属性
            boolean immuneToZombification = true;
            if (config.specialAbilities.containsKey("immune_to_zombification")) {
                immuneToZombification = (Boolean) config.specialAbilities.get("immune_to_zombification");
            }

            if (immuneToZombification) {
                piglin.setImmuneToZombification(true); // 免疫僵尸化
            }

            // 设置装备
            setupEntityEquipment(piglin, config);

            // 启用鲜血猪灵的技能
            enableBloodPiglinSkills(piglin, config);

            // 设置攻击回血配置元数据
            boolean attackHealEnabled = true;
            if (config.specialAbilities.containsKey("attack_heal_enabled")) {
                attackHealEnabled = (Boolean) config.specialAbilities.get("attack_heal_enabled");
            }

            int attackHealPercentage = 10;
            if (config.specialAbilities.containsKey("attack_heal_percentage")) {
                attackHealPercentage = (Integer) config.specialAbilities.get("attack_heal_percentage");
            }

            // 设置元数据标记
            piglin.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            piglin.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            piglin.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            piglin.setMetadata("entityId", new FixedMetadataValue(plugin, "idc15"));
            piglin.setMetadata("bloodPiglin", new FixedMetadataValue(plugin, true));
            piglin.setMetadata("attackHealEnabled", new FixedMetadataValue(plugin, attackHealEnabled));
            piglin.setMetadata("attackHealPercentage", new FixedMetadataValue(plugin, attackHealPercentage));

            if (debugMode) {
                logger.info("成功生成IDC15鲜血猪灵，生命值: " + piglin.getHealth() + "/" + piglin.getMaxHealth() +
                           "，名称: " + piglin.getCustomName() + "，免疫僵尸化: " + piglin.isImmuneToZombification());
            }

            return piglin;

        } catch (Exception e) {
            logger.severe("生成IDC15鲜血猪灵时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用鲜血猪灵的技能（红色粒子球效果 + 爆炸攻击）
     */
    private void enableBloodPiglinSkills(org.bukkit.entity.Piglin piglin, EntityOverrideConfig config) {
        // 启用红色粒子球效果
        boolean particleBallEnabled = true;
        if (config.specialAbilities.containsKey("particle_ball_enabled")) {
            particleBallEnabled = (Boolean) config.specialAbilities.get("particle_ball_enabled");
        }

        // 启用爆炸攻击
        boolean explosionAttackEnabled = true;
        if (config.specialAbilities.containsKey("explosion_attack_enabled")) {
            explosionAttackEnabled = (Boolean) config.specialAbilities.get("explosion_attack_enabled");
        }

        if (particleBallEnabled || explosionAttackEnabled) {
            startBloodPiglinAllSkills(piglin, config, particleBallEnabled, explosionAttackEnabled);
        }

        if (debugMode) {
            logger.info("IDC15鲜血猪灵技能启用状态 - 红色粒子球: " + particleBallEnabled +
                       ", 爆炸攻击: " + explosionAttackEnabled);
        }
    }

    /**
     * 启动鲜血猪灵的所有技能（红色粒子球效果 + 爆炸攻击）
     */
    private void startBloodPiglinAllSkills(org.bukkit.entity.Piglin piglin, EntityOverrideConfig config,
                                          final boolean particleBallEnabled, final boolean explosionAttackEnabled) {

        // 从配置中获取技能参数
        final int particleBallInterval = config.skillCooldownOverrides.getOrDefault("particle_ball_interval", 20); // 默认每1秒
        final double explosionDetectionRange = config.skillCooldownOverrides.getOrDefault("explosion_detection_range", 5).doubleValue(); // 默认5格
        final double explosionDamage = config.skillCooldownOverrides.getOrDefault("explosion_damage", 8).doubleValue(); // 默认8点伤害
        final double explosionRange = config.skillCooldownOverrides.getOrDefault("explosion_range", 2).doubleValue(); // 默认2格
        final double particleBallRadius = config.skillCooldownOverrides.getOrDefault("particle_ball_radius", 2).doubleValue(); // 默认2格（1.5改为2）
        final double projectileSpeed = config.skillCooldownOverrides.getOrDefault("projectile_speed", 1).doubleValue(); // 默认1格/tick（0.5改为1）
        final int projectileMaxSteps = config.skillCooldownOverrides.getOrDefault("projectile_max_steps", 20); // 默认20步

        BukkitRunnable skillTask = new BukkitRunnable() {
            private int tickCounter = 0;

            @Override
            public void run() {
                // 检查实体是否还存在
                if (piglin == null || piglin.isDead() || !piglin.isValid()) {
                    this.cancel();
                    // 清理任务记录
                    particleEffectTasks.remove(piglin);
                    return;
                }

                tickCounter++;

                // 红色粒子球效果（每particleBallInterval tick执行一次）
                if (particleBallEnabled && tickCounter % particleBallInterval == 0) {
                    performRedParticleBall(piglin, particleBallRadius);
                }

                // 爆炸攻击检测（每tick都检测，但有内部冷却）
                if (explosionAttackEnabled) {
                    performExplosionAttack(piglin, explosionDetectionRange, explosionDamage, explosionRange,
                                         projectileSpeed, projectileMaxSteps);
                }
            }
        };

        // 启动任务（每tick执行一次）
        BukkitTask task = skillTask.runTaskTimer(plugin, 60, 1); // 3秒后开始，每tick执行

        // 记录任务以便清理
        particleEffectTasks.put(piglin, task);

        if (debugMode) {
            logger.info("IDC15鲜血猪灵技能任务已启动 - 粒子球间隔: " + particleBallInterval + "tick, 爆炸检测范围: " + explosionDetectionRange + "格");
        }
    }

    /**
     * 执行红色粒子球效果
     */
    private void performRedParticleBall(org.bukkit.entity.Piglin piglin, double particleBallRadius) {
        Location location = piglin.getLocation().add(0, 1, 0);

        // 创建红色粒子球（球形分布）
        for (double theta = 0; theta <= Math.PI; theta += Math.PI / 8) {
            for (double phi = 0; phi <= 2 * Math.PI; phi += Math.PI / 8) {
                double x = particleBallRadius * Math.sin(theta) * Math.cos(phi);
                double y = particleBallRadius * Math.sin(theta) * Math.sin(phi);
                double z = particleBallRadius * Math.cos(theta);

                Location particleLoc = location.clone().add(x, y, z);

                try {
                    // 使用红色粒子
                    piglin.getWorld().spawnParticle(
                            Particle.DUST,
                            particleLoc,
                            1,
                            0, 0, 0,
                            0,
                            new Particle.DustOptions(org.bukkit.Color.RED, 1.0f)
                    );
                } catch (Exception e) {
                    // 如果DUST粒子不可用，使用备用粒子
                    piglin.getWorld().spawnParticle(Particle.HEART, particleLoc, 1, 0, 0, 0, 0);
                }
            }
        }

        if (debugMode) {
            logger.info("IDC15生成红色粒子球，半径: " + particleBallRadius + "格");
        }
    }

    // 爆炸攻击冷却时间记录
    private final Map<LivingEntity, Long> explosionCooldowns = new HashMap<>();

    /**
     * 执行爆炸攻击
     */
    private void performExplosionAttack(org.bukkit.entity.Piglin piglin, double explosionDetectionRange,
                                       double explosionDamage, double explosionRange,
                                       double projectileSpeed, int projectileMaxSteps) {

        // 检查冷却时间（3秒冷却）
        long currentTime = System.currentTimeMillis();
        Long lastExplosion = explosionCooldowns.get(piglin);
        if (lastExplosion != null && currentTime - lastExplosion < 3000) {
            return; // 还在冷却中
        }

        Location location = piglin.getLocation();

        // 检查附近的玩家
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, explosionDetectionRange, explosionDetectionRange, explosionDetectionRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    nearbyPlayers.add(player);
                }
            }
        }

        // 如果有附近的玩家，向第一个玩家发射爆炸弹
        if (!nearbyPlayers.isEmpty()) {
            Player target = nearbyPlayers.get(0); // 选择第一个玩家

            // 记录冷却时间
            explosionCooldowns.put(piglin, currentTime);

            // 计算方向向量
            Vector direction = target.getLocation().subtract(piglin.getLocation()).toVector().normalize();

            // 播放发射音效
            piglin.getWorld().playSound(piglin.getLocation(), Sound.ENTITY_BLAZE_SHOOT, 1.0f, 0.8f);

            // 发射爆炸粒子弹
            new BukkitRunnable() {
                private Location currentLoc = piglin.getLocation().add(0, 1, 0);
                private int steps = 0;

                @Override
                public void run() {
                    if (steps >= projectileMaxSteps) {
                        // 到达最大步数，创建爆炸效果
                        createExplosionEffect(currentLoc, explosionDamage, explosionRange, piglin);
                        this.cancel();
                        return;
                    }

                    // 移动粒子位置
                    currentLoc.add(direction.clone().multiply(projectileSpeed));

                    // 显示红色粒子轨迹
                    try {
                        currentLoc.getWorld().spawnParticle(
                                Particle.DUST,
                                currentLoc,
                                10,
                                0.1, 0.1, 0.1,
                                0.05,
                                new Particle.DustOptions(org.bukkit.Color.RED, 1.5f)
                        );
                    } catch (Exception e) {
                        // 备用粒子
                        currentLoc.getWorld().spawnParticle(Particle.HEART, currentLoc, 5, 0.1, 0.1, 0.1, 0.05);
                    }

                    // 检测碰撞
                    for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 0.5, 0.5, 0.5)) {
                        if (entity instanceof Player && entity != piglin) {
                            // 命中玩家，立即爆炸
                            createExplosionEffect(currentLoc, explosionDamage, explosionRange, piglin);
                            this.cancel();
                            return;
                        }
                    }

                    // 检测方块碰撞
                    Block block = currentLoc.getBlock();
                    if (block.getType().isSolid()) {
                        // 命中方块，立即爆炸
                        createExplosionEffect(currentLoc, explosionDamage, explosionRange, piglin);
                        this.cancel();
                        return;
                    }

                    steps++;
                }
            }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

            if (debugMode) {
                logger.info("IDC15发射爆炸弹，目标: " + target.getName() + "，距离: " + String.format("%.1f", target.getLocation().distance(location)));
            }
        }
    }

    /**
     * 创建爆炸效果
     */
    private void createExplosionEffect(Location location, double explosionDamage, double explosionRange, LivingEntity source) {
        // 创建爆炸效果（不破坏方块）
        location.getWorld().createExplosion(location, 0F, false, false);

        // 对附近玩家造成伤害
        for (Entity entity : location.getWorld().getNearbyEntities(location, explosionRange, explosionRange, explosionRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    // 造成伤害
                    player.damage(explosionDamage, source);

                    // 播放伤害音效
                    player.playSound(player.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);

                    if (debugMode) {
                        logger.info("IDC15爆炸伤害玩家: " + player.getName() + "，伤害: " + explosionDamage);
                    }
                }
            }
        }

        // 显示爆炸粒子效果
        location.getWorld().spawnParticle(Particle.EXPLOSION, location, 5, 0.5, 0.5, 0.5, 0.1);

        // 额外的红色粒子效果
        try {
            location.getWorld().spawnParticle(
                    Particle.DUST,
                    location,
                    50,
                    1.0, 1.0, 1.0,
                    0.1,
                    new Particle.DustOptions(org.bukkit.Color.RED, 2.0f)
            );
        } catch (Exception e) {
            // 备用粒子
            location.getWorld().spawnParticle(Particle.HEART, location, 20, 1.0, 1.0, 1.0, 0.1);
        }

        // 播放爆炸音效
        location.getWorld().playSound(location, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 1.0f);

        if (debugMode) {
            logger.info("IDC15创建爆炸效果，位置: " + location.getBlockX() + "," + location.getBlockY() + "," + location.getBlockZ() +
                       "，伤害: " + explosionDamage + "，范围: " + explosionRange);
        }
    }

    /**
     * 生成IDC16暗影潜影贝（潜影贝形态）
     */
    private LivingEntity spawnShadowShulker(Location location, EntityOverrideConfig config) {
        try {
            // 生成潜影贝实体
            org.bukkit.entity.Shulker shulker = (org.bukkit.entity.Shulker) location.getWorld().spawnEntity(location, EntityType.SHULKER);

            // 设置基础属性
            if (config.healthOverride > 0) {
                shulker.setMaxHealth(config.healthOverride);
                shulker.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                shulker.setCustomName(config.customNameOverride);
                shulker.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置潜影贝颜色
            String shulkerColor = "BLACK";
            if (config.specialAbilities.containsKey("shulker_color")) {
                shulkerColor = (String) config.specialAbilities.get("shulker_color");
            }

            try {
                org.bukkit.DyeColor dyeColor = org.bukkit.DyeColor.valueOf(shulkerColor.toUpperCase());
                shulker.setColor(dyeColor);
            } catch (IllegalArgumentException e) {
                logger.warning("无效的潜影贝颜色: " + shulkerColor + "，使用默认黑色");
                shulker.setColor(org.bukkit.DyeColor.BLACK);
            }

            // 设置药水效果（隐身）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            shulker.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC16添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认隐身效果（1分钟）
                shulker.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 1200, 0));
            }

            // 启用暗影潜影贝的技能
            enableShadowShulkerSkills(shulker, config);

            // 设置元数据标记
            shulker.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            shulker.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            shulker.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            shulker.setMetadata("entityId", new FixedMetadataValue(plugin, "idc16"));
            shulker.setMetadata("shadowShulker", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC16暗影潜影贝，生命值: " + shulker.getHealth() + "/" + shulker.getMaxHealth() +
                           "，名称: " + shulker.getCustomName() + "，颜色: " + shulker.getColor().name());
            }

            return shulker;

        } catch (Exception e) {
            logger.severe("生成IDC16暗影潜影贝时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用暗影潜影贝的技能（三连射子弹攻击 + 受伤混乱物品栏）
     */
    private void enableShadowShulkerSkills(org.bukkit.entity.Shulker shulker, EntityOverrideConfig config) {
        // 启用三连射子弹攻击
        boolean bulletAttackEnabled = true;
        if (config.specialAbilities.containsKey("bullet_attack_enabled")) {
            bulletAttackEnabled = (Boolean) config.specialAbilities.get("bullet_attack_enabled");
        }

        // 启用受伤混乱物品栏
        boolean damageShuffleEnabled = true;
        if (config.specialAbilities.containsKey("damage_shuffle_enabled")) {
            damageShuffleEnabled = (Boolean) config.specialAbilities.get("damage_shuffle_enabled");
        }

        if (bulletAttackEnabled || damageShuffleEnabled) {
            startShadowShulkerAllSkills(shulker, config, bulletAttackEnabled, damageShuffleEnabled);
        }

        if (debugMode) {
            logger.info("IDC16暗影潜影贝技能启用状态 - 三连射子弹: " + bulletAttackEnabled +
                       ", 受伤混乱物品栏: " + damageShuffleEnabled);
        }
    }

    /**
     * 启动暗影潜影贝的所有技能（三连射子弹攻击 + 受伤混乱物品栏）
     */
    private void startShadowShulkerAllSkills(org.bukkit.entity.Shulker shulker, EntityOverrideConfig config,
                                            final boolean bulletAttackEnabled, final boolean damageShuffleEnabled) {

        // 从配置中获取技能参数
        final int bulletAttackInterval = config.skillCooldownOverrides.getOrDefault("bullet_attack_interval", 60); // 默认每3秒
        final int damageCheckInterval = config.skillCooldownOverrides.getOrDefault("damage_check_interval", 20); // 默认每1秒
        final int bulletCount = config.skillCooldownOverrides.getOrDefault("bullet_count", 3); // 默认3颗子弹
        final double attackRange = config.skillCooldownOverrides.getOrDefault("attack_range", 10).doubleValue(); // 默认10格
        final double shuffleRange = config.skillCooldownOverrides.getOrDefault("shuffle_range", 10).doubleValue(); // 默认10格
        final long shuffleCooldown = config.skillCooldownOverrides.getOrDefault("shuffle_cooldown", 5000).longValue(); // 默认5秒
        final double healthThreshold = config.skillCooldownOverrides.getOrDefault("health_threshold", 90).doubleValue() / 100.0; // 默认90%
        final int invisibilityDuration = config.skillCooldownOverrides.getOrDefault("invisibility_duration", 1200); // 默认1分钟

        // 消息发送配置
        final boolean shuffleMessageEnabled;
        if (config.specialAbilities.containsKey("shuffle_message_enabled")) {
            shuffleMessageEnabled = (Boolean) config.specialAbilities.get("shuffle_message_enabled");
        } else {
            shuffleMessageEnabled = false;
        }

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：三连射子弹攻击
        if (bulletAttackEnabled) {
            BukkitTask bulletTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (shulker == null || shulker.isDead() || !shulker.isValid()) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(shulker);
                        return;
                    }

                    performShulkerBulletAttack(shulker, attackRange, bulletCount);
                }
            }.runTaskTimer(plugin, 20, bulletAttackInterval); // 1秒后开始，按配置间隔执行

            tasks.add(bulletTask);
        }

        // 任务2：受伤混乱物品栏检测
        if (damageShuffleEnabled) {
            BukkitTask damageTask = new BukkitRunnable() {
                private long lastShuffleTime = 0;

                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (shulker == null || shulker.isDead() || !shulker.isValid()) {
                        this.cancel();
                        return;
                    }

                    // 检查是否受伤（生命值低于阈值）
                    double currentHealth = shulker.getHealth();
                    double maxHealth = shulker.getMaxHealth();

                    if (currentHealth < maxHealth * healthThreshold &&
                        System.currentTimeMillis() - lastShuffleTime > shuffleCooldown) {

                        performDamageShuffleAttack(shulker, shuffleRange, shuffleMessageEnabled, invisibilityDuration);
                        lastShuffleTime = System.currentTimeMillis();
                    }
                }
            }.runTaskTimer(plugin, 20, damageCheckInterval); // 1秒后开始，按配置间隔检查

            tasks.add(damageTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(shulker, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC16暗影潜影贝技能任务已启动 - 子弹攻击间隔: " + bulletAttackInterval + "tick, 受伤检测间隔: " + damageCheckInterval + "tick");
        }
    }

    /**
     * 执行潜影贝三连射子弹攻击
     */
    private void performShulkerBulletAttack(org.bukkit.entity.Shulker shulker, double attackRange, int bulletCount) {
        Location location = shulker.getLocation();

        // 查找攻击范围内最近的玩家
        Player targetPlayer = null;
        double closestDistance = attackRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        targetPlayer = player;
                    }
                }
            }
        }

        if (targetPlayer != null) {
            final Player target = targetPlayer;

            // 让潜影贝面向目标玩家
            Vector direction = target.getLocation().subtract(location).toVector().normalize();
            location.setDirection(direction);

            // 发射多个潜影贝子弹
            for (int i = 0; i < bulletCount; i++) {
                final int index = i;

                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (shulker == null || shulker.isDead() || !shulker.isValid()) {
                            return;
                        }

                        // 计算子弹发射位置（稍微偏移，使多个子弹不重叠）
                        Location spawnLoc = shulker.getLocation().clone().add(0, 0.5, 0);

                        if (index == 0) {
                            // 中间子弹
                        } else if (index == 1) {
                            // 左侧子弹
                            Vector perpendicular = new Vector(-direction.getZ(), 0, direction.getX()).normalize().multiply(0.5);
                            spawnLoc.add(perpendicular);
                        } else if (index == 2) {
                            // 右侧子弹
                            Vector perpendicular = new Vector(direction.getZ(), 0, -direction.getX()).normalize().multiply(0.5);
                            spawnLoc.add(perpendicular);
                        }

                        // 发射潜影贝子弹
                        org.bukkit.entity.ShulkerBullet bullet = (org.bukkit.entity.ShulkerBullet) shulker.getWorld().spawnEntity(spawnLoc, EntityType.SHULKER_BULLET);
                        bullet.setTarget(target);

                        // 播放发射音效
                        shulker.getWorld().playSound(shulker.getLocation(), Sound.ENTITY_SHULKER_SHOOT, 1.0f, 1.0f);

                        // 添加粒子效果
                        shulker.getWorld().spawnParticle(Particle.PORTAL, spawnLoc, 10, 0.2, 0.2, 0.2, 0.05);

                        if (debugMode) {
                            logger.info("IDC16发射第" + (index + 1) + "颗潜影贝子弹，目标: " + target.getName());
                        }
                    }
                }.runTaskLater(plugin, index * 5); // 每个子弹间隔5tick
            }

            if (debugMode) {
                logger.info("IDC16执行三连射子弹攻击，目标: " + target.getName() + "，距离: " + String.format("%.1f", closestDistance));
            }
        }
    }

    /**
     * 执行受伤混乱物品栏攻击
     */
    private void performDamageShuffleAttack(org.bukkit.entity.Shulker shulker, double shuffleRange,
                                           boolean shuffleMessageEnabled, int invisibilityDuration) {
        Location location = shulker.getLocation();

        // 查找范围内的所有玩家
        List<Player> affectedPlayers = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, shuffleRange, shuffleRange, shuffleRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    affectedPlayers.add(player);
                }
            }
        }

        if (!affectedPlayers.isEmpty()) {
            for (Player player : affectedPlayers) {
                // 混乱玩家的1-9格物品栏
                shufflePlayerHotbar(player);

                // 发送消息给玩家（如果启用）
                if (shuffleMessageEnabled) {
                    // 不发送消息，避免刷屏（已在配置中默认禁用）
                    // player.sendMessage("§8暗影潜影贝的能量扰乱了你的物品栏！");
                }

                // 播放音效
                player.playSound(player.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.5f);

                // 添加粒子效果
                player.getWorld().spawnParticle(Particle.REVERSE_PORTAL, player.getLocation().add(0, 1, 0), 30, 0.5, 1.0, 0.5, 0.05);

                if (debugMode) {
                    logger.info("IDC16混乱玩家物品栏: " + player.getName());
                }
            }

            // 重新隐身
            shulker.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, invisibilityDuration, 0));

            if (debugMode) {
                logger.info("IDC16执行受伤混乱物品栏攻击，影响玩家数量: " + affectedPlayers.size() + "，重新隐身: " + (invisibilityDuration/20) + "秒");
            }
        }
    }

    /**
     * 生成IDC17变异雪傀儡（雪傀儡形态）
     */
    private LivingEntity spawnMutantSnowman(Location location, EntityOverrideConfig config) {
        try {
            // 生成雪傀儡实体
            org.bukkit.entity.Snowman snowman = (org.bukkit.entity.Snowman) location.getWorld().spawnEntity(location, EntityType.SNOW_GOLEM);

            // 设置基础属性
            if (config.healthOverride > 0) {
                snowman.setMaxHealth(config.healthOverride);
                snowman.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                snowman.setCustomName(config.customNameOverride);
                snowman.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度V）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            snowman.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC17添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认速度V效果
                snowman.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 4)); // 速度V
            }

            // 启用变异雪傀儡的技能
            enableMutantSnowmanSkills(snowman, config);

            // 设置元数据标记
            snowman.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            snowman.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            snowman.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            snowman.setMetadata("entityId", new FixedMetadataValue(plugin, "idc17"));
            snowman.setMetadata("mutantSnowman", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC17变异雪傀儡，生命值: " + snowman.getHealth() + "/" + snowman.getMaxHealth() +
                           "，名称: " + snowman.getCustomName());
            }

            return snowman;

        } catch (Exception e) {
            logger.severe("生成IDC17变异雪傀儡时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异雪傀儡的技能（主动追踪玩家 + 连发雪球攻击）
     */
    private void enableMutantSnowmanSkills(org.bukkit.entity.Snowman snowman, EntityOverrideConfig config) {
        // 启用主动追踪玩家
        boolean playerTrackingEnabled = true;
        if (config.specialAbilities.containsKey("player_tracking_enabled")) {
            playerTrackingEnabled = (Boolean) config.specialAbilities.get("player_tracking_enabled");
        }

        // 启用连发雪球攻击
        boolean snowballAttackEnabled = true;
        if (config.specialAbilities.containsKey("snowball_attack_enabled")) {
            snowballAttackEnabled = (Boolean) config.specialAbilities.get("snowball_attack_enabled");
        }

        if (playerTrackingEnabled || snowballAttackEnabled) {
            startMutantSnowmanAllSkills(snowman, config, playerTrackingEnabled, snowballAttackEnabled);
        }

        if (debugMode) {
            logger.info("IDC17变异雪傀儡技能启用状态 - 主动追踪: " + playerTrackingEnabled +
                       ", 连发雪球: " + snowballAttackEnabled);
        }
    }

    /**
     * 启动变异雪傀儡的所有技能（主动追踪玩家 + 连发雪球攻击）
     */
    private void startMutantSnowmanAllSkills(org.bukkit.entity.Snowman snowman, EntityOverrideConfig config,
                                            final boolean playerTrackingEnabled, final boolean snowballAttackEnabled) {

        // 从配置中获取技能参数
        final int trackingInterval = config.skillCooldownOverrides.getOrDefault("tracking_interval", 10); // 默认每0.5秒
        final int snowballAttackInterval = config.skillCooldownOverrides.getOrDefault("snowball_attack_interval", 40); // 默认每2秒
        final double trackingRange = config.skillCooldownOverrides.getOrDefault("tracking_range", 20).doubleValue(); // 默认20格
        final double snowballRange = config.skillCooldownOverrides.getOrDefault("snowball_range", 15).doubleValue(); // 默认15格
        final double meleeRange = config.skillCooldownOverrides.getOrDefault("melee_range", 3).doubleValue(); // 默认3格
        final double meleeDamage = config.skillCooldownOverrides.getOrDefault("melee_damage", 4).doubleValue(); // 默认4点伤害
        final int snowballCount = config.skillCooldownOverrides.getOrDefault("snowball_count", 8); // 默认8个雪球
        final int snowballInterval = config.skillCooldownOverrides.getOrDefault("snowball_interval", 3); // 默认3tick间隔
        final double spreadFactor = config.skillCooldownOverrides.getOrDefault("spread_factor", 0).doubleValue() / 10.0; // 默认0.1（配置为1）
        final double snowballSpeed = config.skillCooldownOverrides.getOrDefault("snowball_speed", 2).doubleValue() / 10.0 * 10; // 默认1.5（配置为15）

        // 毒性雪球配置
        final boolean poisonSnowballEnabled;
        if (config.specialAbilities.containsKey("poison_snowball_enabled")) {
            poisonSnowballEnabled = (Boolean) config.specialAbilities.get("poison_snowball_enabled");
        } else {
            poisonSnowballEnabled = true;
        }

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：主动追踪玩家
        if (playerTrackingEnabled) {
            BukkitTask trackingTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (snowman == null || snowman.isDead() || !snowman.isValid()) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(snowman);
                        return;
                    }

                    performPlayerTracking(snowman, trackingRange, meleeRange, meleeDamage);
                }
            }.runTaskTimer(plugin, 5, trackingInterval); // 0.25秒后开始，按配置间隔执行

            tasks.add(trackingTask);
        }

        // 任务2：连发雪球攻击
        if (snowballAttackEnabled) {
            BukkitTask snowballTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (snowman == null || snowman.isDead() || !snowman.isValid()) {
                        this.cancel();
                        return;
                    }

                    performSnowballAttack(snowman, snowballRange, snowballCount, snowballInterval,
                                        spreadFactor, snowballSpeed, poisonSnowballEnabled);
                }
            }.runTaskTimer(plugin, 20, snowballAttackInterval); // 1秒后开始，按配置间隔执行

            tasks.add(snowballTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(snowman, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC17变异雪傀儡技能任务已启动 - 追踪间隔: " + trackingInterval + "tick, 雪球攻击间隔: " + snowballAttackInterval + "tick");
        }
    }

    /**
     * 执行主动追踪玩家
     */
    private void performPlayerTracking(org.bukkit.entity.Snowman snowman, double trackingRange,
                                      double meleeRange, double meleeDamage) {
        Location location = snowman.getLocation();

        // 查找追踪范围内最近的玩家
        Player nearestPlayer = null;
        double minDistance = trackingRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, trackingRange, trackingRange, trackingRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestPlayer = player;
                    }
                }
            }
        }

        if (nearestPlayer != null) {
            // 计算方向向量
            Vector direction = nearestPlayer.getLocation().toVector().subtract(location.toVector()).normalize();

            // 让雪傀儡面向玩家
            Location lookLocation = location.clone();
            lookLocation.setDirection(direction);
            snowman.teleport(lookLocation);

            // 设置雪傀儡的路径目标
            if (minDistance > meleeRange) {
                // 如果距离较远，移动向玩家
                snowman.getPathfinder().moveTo(nearestPlayer, 1.2); // 增加速度
            } else {
                // 如果距离较近，直接攻击
                snowman.getPathfinder().moveTo(nearestPlayer, 1.0);

                // 近距离攻击玩家
                nearestPlayer.damage(meleeDamage, snowman);

                // 播放攻击音效
                snowman.getWorld().playSound(snowman.getLocation(), Sound.ENTITY_SNOW_GOLEM_HURT, 1.0f, 0.8f);

                if (debugMode) {
                    logger.info("IDC17近战攻击玩家: " + nearestPlayer.getName() + "，伤害: " + meleeDamage);
                }
            }

            if (debugMode && minDistance <= meleeRange) {
                logger.info("IDC17追踪玩家: " + nearestPlayer.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 执行连发雪球攻击
     */
    private void performSnowballAttack(org.bukkit.entity.Snowman snowman, double snowballRange,
                                      int snowballCount, int snowballInterval, double spreadFactor,
                                      double snowballSpeed, boolean poisonSnowballEnabled) {
        Location location = snowman.getLocation();

        // 查找雪球攻击范围内最近的玩家
        Player target = null;
        double minDistance = snowballRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, snowballRange, snowballRange, snowballRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            final Player finalTarget = target;

            // 播放准备攻击音效
            snowman.getWorld().playSound(snowman.getLocation(), Sound.ENTITY_SNOW_GOLEM_SHOOT, 1.0f, 0.8f);

            // 连发雪球攻击 - 发射多个雪球
            for (int i = 0; i < snowballCount; i++) {
                final int index = i;
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        if (snowman == null || !snowman.isValid() || snowman.isDead()) {
                            return;
                        }

                        // 计算方向向量（添加一些随机偏移，模拟散射）
                        Location snowmanEyeLocation = snowman.getLocation().add(0, 1.0, 0);
                        Vector direction = finalTarget.getEyeLocation().toVector()
                                .subtract(snowmanEyeLocation.toVector())
                                .normalize();

                        // 添加随机偏移
                        Random random = new Random();
                        direction.add(new Vector(
                                (random.nextDouble() - 0.5) * spreadFactor,
                                (random.nextDouble() - 0.5) * spreadFactor,
                                (random.nextDouble() - 0.5) * spreadFactor
                        )).normalize();

                        // 发射雪球
                        org.bukkit.entity.Snowball snowball = snowman.launchProjectile(org.bukkit.entity.Snowball.class, direction.multiply(snowballSpeed));

                        // 添加元数据，用于识别这是变异雪傀儡的雪球
                        snowball.setMetadata("mutantSnowmanSnowball", new FixedMetadataValue(plugin, true));

                        // 添加毒性效果到雪球（如果启用）
                        if (poisonSnowballEnabled) {
                            snowball.setMetadata("poisonSnowball", new FixedMetadataValue(plugin, true));
                        }

                        // 添加粒子效果
                        snowmanEyeLocation.getWorld().spawnParticle(Particle.SNOWFLAKE, snowmanEyeLocation, 5, 0.1, 0.1, 0.1, 0.01);

                        // 播放发射音效
                        snowman.getWorld().playSound(snowman.getLocation(),
                                Sound.ENTITY_SNOWBALL_THROW,
                                0.5f, 1.0f + (index * 0.1f)); // 音调随发射次数变化

                        if (debugMode) {
                            logger.info("IDC17发射第" + (index + 1) + "个雪球，目标: " + finalTarget.getName());
                        }
                    }
                }.runTaskLater(plugin, index * snowballInterval); // 按配置间隔发射雪球
            }

            if (debugMode) {
                logger.info("IDC17执行连发雪球攻击，目标: " + finalTarget.getName() + "，雪球数量: " + snowballCount);
            }
        }
    }

    /**
     * 生成IDC18变异铁傀儡（铁傀儡形态）
     */
    private LivingEntity spawnMutantIronGolem(Location location, EntityOverrideConfig config) {
        try {
            // 生成铁傀儡实体
            org.bukkit.entity.IronGolem ironGolem = (org.bukkit.entity.IronGolem) location.getWorld().spawnEntity(location, EntityType.IRON_GOLEM);

            // 设置基础属性
            if (config.healthOverride > 0) {
                ironGolem.setMaxHealth(config.healthOverride);
                ironGolem.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                ironGolem.setCustomName(config.customNameOverride);
                ironGolem.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度IV + 跳跃提升IV）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            ironGolem.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC18添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认效果
                ironGolem.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 3)); // 速度IV
                ironGolem.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 3)); // 跳跃提升IV
            }

            // 启用敌对AI（如果配置启用）
            boolean hostileAiEnabled = true;
            if (config.specialAbilities.containsKey("hostile_ai_enabled")) {
                hostileAiEnabled = (Boolean) config.specialAbilities.get("hostile_ai_enabled");
            }

            if (hostileAiEnabled) {
                // 启用敌对AI，确保主动攻击玩家而不是其他生物
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getHostileAIManager() != null) {
                        dzPlugin.getHostileAIManager().enableHostileAI(ironGolem);
                        if (debugMode) {
                            logger.info("已为IDC18变异铁傀儡启用敌对AI");
                        }
                    }
                }
            }

            // 启用变异铁傀儡的技能
            enableMutantIronGolemSkills(ironGolem, config);

            // 设置元数据标记
            ironGolem.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            ironGolem.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            ironGolem.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            ironGolem.setMetadata("entityId", new FixedMetadataValue(plugin, "idc18"));
            ironGolem.setMetadata("mutantIronGolem", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC18变异铁傀儡，生命值: " + ironGolem.getHealth() + "/" + ironGolem.getMaxHealth() +
                           "，名称: " + ironGolem.getCustomName());
            }

            return ironGolem;

        } catch (Exception e) {
            logger.severe("生成IDC18变异铁傀儡时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异铁傀儡的技能（主动追踪玩家 + 远程声波弹攻击 + 草方块攻击）
     */
    private void enableMutantIronGolemSkills(org.bukkit.entity.IronGolem ironGolem, EntityOverrideConfig config) {
        // 启用主动追踪玩家
        boolean playerTrackingEnabled = true;
        if (config.specialAbilities.containsKey("player_tracking_enabled")) {
            playerTrackingEnabled = (Boolean) config.specialAbilities.get("player_tracking_enabled");
        }

        // 启用远程声波弹攻击
        boolean sonicAttackEnabled = true;
        if (config.specialAbilities.containsKey("sonic_attack_enabled")) {
            sonicAttackEnabled = (Boolean) config.specialAbilities.get("sonic_attack_enabled");
        }

        // 启用草方块攻击
        boolean grassAttackEnabled = true;
        if (config.specialAbilities.containsKey("grass_attack_enabled")) {
            grassAttackEnabled = (Boolean) config.specialAbilities.get("grass_attack_enabled");
        }

        if (playerTrackingEnabled || sonicAttackEnabled || grassAttackEnabled) {
            startMutantIronGolemAllSkills(ironGolem, config, playerTrackingEnabled, sonicAttackEnabled, grassAttackEnabled);
        }

        if (debugMode) {
            logger.info("IDC18变异铁傀儡技能启用状态 - 主动追踪: " + playerTrackingEnabled +
                       ", 声波攻击: " + sonicAttackEnabled + ", 草方块攻击: " + grassAttackEnabled);
        }
    }

    /**
     * 启动变异铁傀儡的所有技能（主动追踪玩家 + 远程声波弹攻击 + 草方块攻击）
     */
    private void startMutantIronGolemAllSkills(org.bukkit.entity.IronGolem ironGolem, EntityOverrideConfig config,
                                              final boolean playerTrackingEnabled, final boolean sonicAttackEnabled,
                                              final boolean grassAttackEnabled) {

        // 从配置中获取技能参数
        final int trackingInterval = config.skillCooldownOverrides.getOrDefault("tracking_interval", 5); // 默认每0.25秒
        final int sonicAttackInterval = config.skillCooldownOverrides.getOrDefault("sonic_attack_interval", 80); // 默认每4秒
        final int grassAttackInterval = config.skillCooldownOverrides.getOrDefault("grass_attack_interval", 100); // 默认每5秒
        final double trackingRange = config.skillCooldownOverrides.getOrDefault("tracking_range", 30).doubleValue(); // 默认30格
        final double attackRange = config.skillCooldownOverrides.getOrDefault("attack_range", 25).doubleValue(); // 默认25格
        final double meleeRange = config.skillCooldownOverrides.getOrDefault("melee_range", 4).doubleValue(); // 默认4格
        final double meleeDamage = config.skillCooldownOverrides.getOrDefault("melee_damage", 15).doubleValue(); // 默认15点伤害
        final double knockbackStrength = config.skillCooldownOverrides.getOrDefault("knockback_strength", 2).doubleValue(); // 默认2.0
        final double knockbackHeight = config.skillCooldownOverrides.getOrDefault("knockback_height", 1).doubleValue() / 10.0 * 8; // 默认0.8（配置为8）

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：主动追踪玩家
        if (playerTrackingEnabled) {
            BukkitTask trackingTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (ironGolem == null || ironGolem.isDead() || !ironGolem.isValid()) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(ironGolem);
                        return;
                    }

                    performIronGolemPlayerTracking(ironGolem, trackingRange, meleeRange, meleeDamage,
                                                  knockbackStrength, knockbackHeight);
                }
            }.runTaskTimer(plugin, 5, trackingInterval); // 0.25秒后开始，按配置间隔执行

            tasks.add(trackingTask);
        }

        // 任务2：远程声波弹攻击
        if (sonicAttackEnabled) {
            BukkitTask sonicTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (ironGolem == null || ironGolem.isDead() || !ironGolem.isValid()) {
                        this.cancel();
                        return;
                    }

                    performSonicBulletAttack(ironGolem, attackRange);
                }
            }.runTaskTimer(plugin, 60, sonicAttackInterval); // 3秒后开始，按配置间隔执行

            tasks.add(sonicTask);
        }

        // 任务3：草方块攻击
        if (grassAttackEnabled) {
            BukkitTask grassTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (ironGolem == null || ironGolem.isDead() || !ironGolem.isValid()) {
                        this.cancel();
                        return;
                    }

                    performGrassBlockAttack(ironGolem, attackRange);
                }
            }.runTaskTimer(plugin, 100, grassAttackInterval); // 5秒后开始，按配置间隔执行

            tasks.add(grassTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(ironGolem, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC18变异铁傀儡技能任务已启动 - 追踪间隔: " + trackingInterval + "tick, 声波攻击间隔: " + sonicAttackInterval + "tick, 草方块攻击间隔: " + grassAttackInterval + "tick");
        }
    }

    /**
     * 执行铁傀儡主动追踪玩家
     */
    private void performIronGolemPlayerTracking(org.bukkit.entity.IronGolem ironGolem, double trackingRange,
                                               double meleeRange, double meleeDamage,
                                               double knockbackStrength, double knockbackHeight) {
        Location location = ironGolem.getLocation();

        // 查找追踪范围内最近的玩家
        Player nearestPlayer = null;
        double minDistance = trackingRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, trackingRange, trackingRange, trackingRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestPlayer = player;
                    }
                }
            }
        }

        if (nearestPlayer != null) {
            // 计算方向向量
            Vector direction = nearestPlayer.getLocation().toVector().subtract(location.toVector()).normalize();

            // 让铁傀儡面向玩家
            Location lookLocation = location.clone();
            lookLocation.setDirection(direction);
            ironGolem.teleport(lookLocation);

            // 设置铁傀儡的路径目标 - 更积极地追踪玩家
            ironGolem.getPathfinder().moveTo(nearestPlayer, 1.5);

            // 如果距离较近，直接攻击
            if (minDistance <= meleeRange) {
                // 近距离攻击玩家
                nearestPlayer.damage(meleeDamage, ironGolem);

                // 击飞效果
                Vector knockback = direction.clone().multiply(knockbackStrength).setY(knockbackHeight);
                nearestPlayer.setVelocity(knockback);

                // 播放攻击音效
                ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_IRON_GOLEM_ATTACK, 1.0f, 0.8f);

                if (debugMode) {
                    logger.info("IDC18近战攻击玩家: " + nearestPlayer.getName() + "，伤害: " + meleeDamage + "，击飞强度: " + knockbackStrength);
                }
            }

            if (debugMode && minDistance > meleeRange) {
                logger.info("IDC18追踪玩家: " + nearestPlayer.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 执行声波弹攻击
     */
    private void performSonicBulletAttack(org.bukkit.entity.IronGolem ironGolem, double attackRange) {
        Location location = ironGolem.getLocation();

        // 查找攻击范围内最近的玩家
        Player target = null;
        double minDistance = attackRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 播放声波攻击音效
            ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.5f);

            // 计算方向向量
            Vector direction = target.getEyeLocation().toVector()
                    .subtract(ironGolem.getLocation().add(0, 1.0, 0).toVector())
                    .normalize();

            // 发射声波弹（使用粒子轨迹模拟）
            new BukkitRunnable() {
                private Location currentLoc = ironGolem.getLocation().add(0, 1.0, 0);
                private int steps = 0;
                private final int maxSteps = 50; // 最大飞行步数
                private final double speed = 0.8; // 飞行速度

                @Override
                public void run() {
                    if (steps >= maxSteps) {
                        this.cancel();
                        return;
                    }

                    // 移动声波弹位置
                    currentLoc.add(direction.clone().multiply(speed));

                    // 显示声波粒子轨迹
                    currentLoc.getWorld().spawnParticle(Particle.SONIC_BOOM, currentLoc, 1, 0, 0, 0, 0);
                    currentLoc.getWorld().spawnParticle(Particle.SWEEP_ATTACK, currentLoc, 3, 0.2, 0.2, 0.2, 0.05);

                    // 检测碰撞
                    for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != ironGolem) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害（与原版一致：20点伤害）
                            hitPlayer.damage(20.0, ironGolem);

                            // 击飞效果
                            Vector knockback = direction.clone().multiply(1.5).setY(0.6);
                            hitPlayer.setVelocity(knockback);

                            // 播放命中音效
                            hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            if (debugMode) {
                                logger.info("IDC18声波弹命中玩家: " + hitPlayer.getName() + "，伤害: 12.0");
                            }

                            this.cancel();
                            return;
                        }
                    }

                    // 检测方块碰撞
                    Block block = currentLoc.getBlock();
                    if (block.getType().isSolid()) {
                        // 命中方块，创建爆炸效果
                        currentLoc.getWorld().spawnParticle(Particle.EXPLOSION, currentLoc, 5, 0.5, 0.5, 0.5, 0.1);
                        currentLoc.getWorld().playSound(currentLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.5f, 1.2f);
                        this.cancel();
                        return;
                    }

                    steps++;
                }
            }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

            if (debugMode) {
                logger.info("IDC18发射声波弹，目标: " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 执行草方块攻击（发射真实草方块延伸攻击）
     */
    private void performGrassBlockAttack(org.bukkit.entity.IronGolem ironGolem, double attackRange) {
        Location location = ironGolem.getLocation();

        // 查找攻击范围内最近的玩家
        Player target = null;
        double minDistance = attackRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 播放草方块攻击音效
            ironGolem.getWorld().playSound(ironGolem.getLocation(), Sound.BLOCK_GRASS_BREAK, 1.0f, 0.5f);

            // 发射真实草方块延伸攻击
            shootRealGrassBlocks(ironGolem, target);

            if (debugMode) {
                logger.info("IDC18发射草方块攻击，目标: " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 发射真实草方块延伸攻击（与原版一致）
     */
    private void shootRealGrassBlocks(org.bukkit.entity.IronGolem ironGolem, Player target) {
        final Location start = ironGolem.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建草方块攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 20; // 最大射程减小（与原版一致）
            private final List<Location> blockLocations = new ArrayList<>();

            @Override
            public void run() {
                if (ironGolem == null || !ironGolem.isValid() || ironGolem.isDead() || distance >= maxDistance) {
                    // 延伸完成，1秒后移除所有草方块
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.GRASS_BLOCK) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除草方块

                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.GRASS_BLOCK) {
                    // 如果碰到墙壁，停止延伸
                    this.cancel();

                    // 播放碰撞音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 移除所有草方块 - 延伸完成1秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.GRASS_BLOCK) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.CRIT, loc, 10, 0.5, 0.5, 0.5, 0.1);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后移除草方块

                    return;
                }

                // 在当前位置创建草方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.GRASS_BLOCK);
                    blockLocations.add(currentLoc.clone());

                    // 播放草方块生成音效
                    world.playSound(currentLoc, Sound.BLOCK_GRASS_PLACE, 0.5f, 1.0f);

                    // 检测是否击中玩家
                    for (Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != ironGolem) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害（与原版一致：44点伤害）
                            hitPlayer.damage(44.0, ironGolem);

                            // 禁止移动1秒（通过给予缓慢效果实现）
                            hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20, 100)); // 100级缓慢，相当于无法移动

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);

                            if (debugMode) {
                                logger.info("IDC18草方块攻击命中玩家: " + hitPlayer.getName() + "，伤害: 44.0，缓慢1秒");
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（最高速度）
    }

    /**
     * 生成IDC19变异僵尸Max（溺尸形态）
     */
    private LivingEntity spawnMutantZombieMax(Location location, EntityOverrideConfig config) {
        try {
            // 生成溺尸实体
            org.bukkit.entity.Drowned drowned = (org.bukkit.entity.Drowned) location.getWorld().spawnEntity(location, EntityType.DROWNED);

            // 设置基础属性
            if (config.healthOverride > 0) {
                drowned.setMaxHealth(config.healthOverride);
                drowned.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                drowned.setCustomName(config.customNameOverride);
                drowned.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置装备（雷霆三叉戟）
            org.bukkit.inventory.ItemStack trident = new org.bukkit.inventory.ItemStack(Material.TRIDENT);
            org.bukkit.inventory.meta.ItemMeta tridentMeta = trident.getItemMeta();
            tridentMeta.setDisplayName("§b雷霆三叉戟");
            tridentMeta.addEnchant(Enchantment.CHANNELING, 20, true); // 引雷20级
            tridentMeta.addEnchant(Enchantment.LOYALTY, 10, true); // 忠诚10级
            tridentMeta.addEnchant(Enchantment.PIERCING, 4, true); // 穿透4级
            trident.setItemMeta(tridentMeta);

            drowned.getEquipment().setItemInMainHand(trident);
            drowned.getEquipment().setItemInMainHandDropChance(0.0F); // 不掉落

            if (debugMode) {
                logger.info("为IDC19设置雷霆三叉戟装备");
            }

            // 设置药水效果（速度IX + 跳跃提升III）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            drowned.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC19添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认效果
                drowned.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 8)); // 速度IX
                drowned.addPotionEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, Integer.MAX_VALUE, 2)); // 跳跃提升III
            }

            // 启用变异僵尸Max的技能
            enableMutantZombieMaxSkills(drowned, config);

            // 设置元数据标记
            drowned.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            drowned.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            drowned.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            drowned.setMetadata("entityId", new FixedMetadataValue(plugin, "idc19"));
            drowned.setMetadata("mutantZombieMax", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC19变异僵尸Max，生命值: " + drowned.getHealth() + "/" + drowned.getMaxHealth() +
                           "，名称: " + drowned.getCustomName());
            }

            return drowned;

        } catch (Exception e) {
            logger.severe("生成IDC19变异僵尸Max时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用变异僵尸Max的技能（五重技能系统）
     */
    private void enableMutantZombieMaxSkills(org.bukkit.entity.Drowned drowned, EntityOverrideConfig config) {
        // 启用天气控制
        boolean weatherControlEnabled = true;
        if (config.specialAbilities.containsKey("weather_control_enabled")) {
            weatherControlEnabled = (Boolean) config.specialAbilities.get("weather_control_enabled");
        }

        // 启用粒子攻击
        boolean particleAttackEnabled = true;
        if (config.specialAbilities.containsKey("particle_attack_enabled")) {
            particleAttackEnabled = (Boolean) config.specialAbilities.get("particle_attack_enabled");
        }

        // 启用三叉戟攻击
        boolean tridentAttackEnabled = true;
        if (config.specialAbilities.containsKey("trident_attack_enabled")) {
            tridentAttackEnabled = (Boolean) config.specialAbilities.get("trident_attack_enabled");
        }

        // 启用闪电攻击
        boolean lightningAttackEnabled = true;
        if (config.specialAbilities.containsKey("lightning_attack_enabled")) {
            lightningAttackEnabled = (Boolean) config.specialAbilities.get("lightning_attack_enabled");
        }

        // 启用隐身技能
        boolean invisibilityEnabled = true;
        if (config.specialAbilities.containsKey("invisibility_enabled")) {
            invisibilityEnabled = (Boolean) config.specialAbilities.get("invisibility_enabled");
        }

        // 启用传送能力
        boolean teleportEnabled = true;
        if (config.specialAbilities.containsKey("teleport_enabled")) {
            teleportEnabled = (Boolean) config.specialAbilities.get("teleport_enabled");
        }

        if (weatherControlEnabled || particleAttackEnabled || tridentAttackEnabled ||
            invisibilityEnabled || teleportEnabled) {
            startMutantZombieMaxAllSkills(drowned, config, weatherControlEnabled, particleAttackEnabled,
                                        tridentAttackEnabled, lightningAttackEnabled, invisibilityEnabled, teleportEnabled);
        }

        if (debugMode) {
            logger.info("IDC19变异僵尸Max技能启用状态 - 天气控制: " + weatherControlEnabled +
                       ", 粒子攻击: " + particleAttackEnabled + ", 三叉戟攻击: " + tridentAttackEnabled +
                       ", 闪电攻击: " + lightningAttackEnabled + ", 隐身: " + invisibilityEnabled +
                       ", 传送: " + teleportEnabled);
        }
    }

    /**
     * 启动变异僵尸Max的所有技能（五重技能系统）
     */
    private void startMutantZombieMaxAllSkills(org.bukkit.entity.Drowned drowned, EntityOverrideConfig config,
                                              final boolean weatherControlEnabled, final boolean particleAttackEnabled,
                                              final boolean tridentAttackEnabled, final boolean lightningAttackEnabled,
                                              final boolean invisibilityEnabled, final boolean teleportEnabled) {

        // 从配置中获取技能参数
        final int weatherControlInterval = config.skillCooldownOverrides.getOrDefault("weather_control_interval", 1200); // 默认1分钟
        final int particleEffectInterval = config.skillCooldownOverrides.getOrDefault("particle_effect_interval", 10); // 默认0.5秒
        final int tridentAttackInterval = config.skillCooldownOverrides.getOrDefault("trident_attack_interval", 20); // 默认1秒
        final int invisibilityInterval = config.skillCooldownOverrides.getOrDefault("invisibility_interval", 100); // 默认5秒
        final int teleportInterval = config.skillCooldownOverrides.getOrDefault("teleport_interval", 40); // 默认2秒

        final double particleRange = config.skillCooldownOverrides.getOrDefault("particle_range", 15).doubleValue(); // 默认15格
        final double tridentRange = config.skillCooldownOverrides.getOrDefault("trident_range", 20).doubleValue(); // 默认20格
        final double teleportRange = config.skillCooldownOverrides.getOrDefault("teleport_range", 20).doubleValue(); // 默认20格
        final double teleportDistanceMin = config.skillCooldownOverrides.getOrDefault("teleport_distance_min", 3).doubleValue(); // 默认3格
        final double teleportDistanceMax = config.skillCooldownOverrides.getOrDefault("teleport_distance_max", 5).doubleValue(); // 默认5格

        final double particleDamage = config.skillCooldownOverrides.getOrDefault("particle_damage", 8).doubleValue(); // 默认8点
        final double tridentDamage = config.skillCooldownOverrides.getOrDefault("trident_damage", 10).doubleValue(); // 默认10点
        final int invisibilityDuration = config.skillCooldownOverrides.getOrDefault("invisibility_duration", 60); // 默认3秒

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：天气控制
        if (weatherControlEnabled) {
            BukkitTask weatherTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (drowned == null || drowned.isDead() || !drowned.isValid()) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(drowned);
                        return;
                    }

                    performWeatherControl(drowned);
                }
            }.runTaskTimer(plugin, 0, weatherControlInterval); // 立即开始，按配置间隔执行

            tasks.add(weatherTask);
        }

        // 任务2：蓝色粒子环绕+发射
        if (particleAttackEnabled) {
            BukkitTask particleTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (drowned == null || drowned.isDead() || !drowned.isValid()) {
                        this.cancel();
                        return;
                    }

                    performParticleAttack(drowned, particleRange, particleDamage);
                }
            }.runTaskTimer(plugin, 0, particleEffectInterval); // 立即开始，按配置间隔执行

            tasks.add(particleTask);
        }

        // 任务3：三叉戟发射+闪电攻击
        if (tridentAttackEnabled) {
            BukkitTask tridentTask = new BukkitRunnable() {
                private int shotCount = 0;

                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (drowned == null || drowned.isDead() || !drowned.isValid()) {
                        this.cancel();
                        return;
                    }

                    performTridentAttack(drowned, tridentRange, tridentDamage, lightningAttackEnabled, shotCount);
                    shotCount++;
                }
            }.runTaskTimer(plugin, 40, tridentAttackInterval); // 2秒后开始，按配置间隔执行

            tasks.add(tridentTask);
        }

        // 任务4：隐身技能
        if (invisibilityEnabled) {
            BukkitTask invisibilityTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (drowned == null || drowned.isDead() || !drowned.isValid()) {
                        this.cancel();
                        return;
                    }

                    performInvisibilitySkill(drowned, invisibilityDuration);
                }
            }.runTaskTimer(plugin, 100, invisibilityInterval); // 5秒后开始，按配置间隔执行

            tasks.add(invisibilityTask);
        }

        // 任务5：传送能力
        if (teleportEnabled) {
            BukkitTask teleportTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (drowned == null || drowned.isDead() || !drowned.isValid()) {
                        this.cancel();
                        return;
                    }

                    performTeleportSkill(drowned, teleportRange, teleportDistanceMin, teleportDistanceMax);
                }
            }.runTaskTimer(plugin, 40, teleportInterval); // 2秒后开始，按配置间隔执行

            tasks.add(teleportTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(drowned, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC19变异僵尸Max技能任务已启动 - 天气控制: " + weatherControlEnabled +
                       ", 粒子攻击: " + particleAttackEnabled + ", 三叉戟攻击: " + tridentAttackEnabled +
                       ", 隐身: " + invisibilityEnabled + ", 传送: " + teleportEnabled);
        }
    }

    /**
     * 执行天气控制（设置雷暴天气）
     */
    private void performWeatherControl(org.bukkit.entity.Drowned drowned) {
        // 获取溺尸所在的世界
        World world = drowned.getWorld();

        // 设置天气为雷暴
        world.setStorm(true);
        world.setThundering(true);

        // 设置雷暴持续时间为较长时间（6000 ticks = 5分钟）
        world.setWeatherDuration(6000);
        world.setThunderDuration(6000);

        if (debugMode) {
            logger.info("IDC19变异僵尸Max控制天气：设置雷暴天气，持续5分钟");
        }
    }

    /**
     * 执行蓝色粒子环绕+发射攻击
     */
    private void performParticleAttack(org.bukkit.entity.Drowned drowned, double particleRange, double particleDamage) {
        // 获取溺尸位置
        Location location = drowned.getLocation().add(0, 1, 0);

        // 创建蓝色粒子环绕效果（3层粒子环）
        for (int i = 0; i < 3; i++) { // 创建3层粒子环
            double y = i * 0.5; // 每层高度间隔0.5格
            for (int j = 0; j < 12; j++) { // 每层12个点
                double angle = Math.toRadians(j * 30);
                double radius = 1.0 + i * 0.2; // 半径随高度略微增加
                double x = radius * Math.cos(angle);
                double z = radius * Math.sin(angle);

                Location particleLoc = location.clone().add(x, y, z);

                // 生成蓝色粒子（灵魂火焰粒子）
                location.getWorld().spawnParticle(Particle.SOUL_FIRE_FLAME, particleLoc, 1, 0.0, 0.0, 0.0, 0.0);
            }
        }

        // 检测附近玩家，发射蓝色粒子
        for (Entity entity : drowned.getNearbyEntities(particleRange, particleRange, particleRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {

                    // 获取从溺尸到玩家的方向向量
                    Vector direction = player.getLocation().subtract(drowned.getLocation()).toVector().normalize();

                    // 发射蓝色粒子轨迹
                    Location startLoc = drowned.getLocation().add(0, 1.5, 0);
                    for (int i = 0; i < 20; i++) {
                        Location particleLoc = startLoc.clone().add(direction.clone().multiply(i * 0.5));
                        startLoc.getWorld().spawnParticle(Particle.SOUL_FIRE_FLAME, particleLoc, 1, 0.05, 0.05, 0.05, 0.0);

                        // 检测是否击中玩家
                        if (player.getLocation().distance(particleLoc) < 1.5) {
                            // 造成伤害
                            player.damage(particleDamage, drowned);

                            // 计算击退方向（从粒子位置指向玩家的反方向）
                            Vector knockbackDir = player.getLocation().subtract(particleLoc).toVector().normalize().multiply(2.0);

                            // 应用击退效果
                            player.setVelocity(knockbackDir);

                            // 播放击中音效
                            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_WITHER_BREAK_BLOCK, 1.0f, 1.0f);

                            // 显示击中粒子效果
                            player.getWorld().spawnParticle(Particle.EXPLOSION, player.getLocation(), 5, 0.3, 0.3, 0.3, 0.1);

                            if (debugMode) {
                                logger.info("IDC19粒子攻击命中玩家: " + player.getName() + "，伤害: " + particleDamage);
                            }

                            break; // 击中后停止粒子轨迹
                        }
                    }
                }
            }
        }
    }

    /**
     * 执行三叉戟发射+闪电攻击
     */
    private void performTridentAttack(org.bukkit.entity.Drowned drowned, double tridentRange, double tridentDamage,
                                     boolean lightningAttackEnabled, int shotCount) {
        // 寻找最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : drowned.getNearbyEntities(tridentRange, tridentRange, tridentRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = player.getLocation().distance(drowned.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        // 如果找到目标玩家
        if (target != null) {
            // 让溺尸面向目标
            Location drownedLoc = drowned.getLocation();
            Vector direction = target.getLocation().subtract(drownedLoc).toVector().normalize();
            drownedLoc.setDirection(direction);
            drowned.teleport(drownedLoc);

            // 每发射2次后使用闪电攻击
            if (lightningAttackEnabled && shotCount % 2 == 0) {
                // 生成闪电攻击
                target.getWorld().strikeLightning(target.getLocation());

                // 播放闪电音效
                drowned.getWorld().playSound(drowned.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 1.0f, 1.0f);

                if (debugMode) {
                    logger.info("IDC19闪电攻击目标: " + target.getName());
                }
            } else {
                // 播放发射音效
                drowned.getWorld().playSound(drowned.getLocation(), Sound.ITEM_TRIDENT_THROW, 1.0f, 0.8f);

                // 发射粒子效果模拟三叉戟
                final Player finalTarget = target;
                Vector velocity = direction.multiply(2.0);
                Location startLoc = drowned.getLocation().add(0, 1.5, 0);

                new BukkitRunnable() {
                    private int ticks = 0;
                    private final int maxTicks = 40; // 最大追踪时间
                    private Location currentLoc;
                    private boolean hasHit = false;

                    @Override
                    public void run() {
                        if (ticks == 0) {
                            currentLoc = startLoc.clone();
                        }

                        ticks++;

                        if (ticks >= maxTicks || hasHit) {
                            this.cancel();
                            return;
                        }

                        // 更新位置
                        currentLoc.add(velocity.clone().multiply(0.5)); // 每tick移动0.5个单位

                        // 在当前位置生成蓝色粒子
                        currentLoc.getWorld().spawnParticle(Particle.SOUL_FIRE_FLAME, currentLoc, 5, 0.1, 0.1, 0.1, 0.0);

                        // 检测是否击中玩家
                        for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                            if (entity instanceof Player && entity.getLocation().distance(currentLoc) < 1.5) {
                                Player player = (Player) entity;

                                // 造成伤害
                                player.damage(tridentDamage, drowned);

                                // 播放击中音效
                                player.getWorld().playSound(player.getLocation(), Sound.ITEM_TRIDENT_HIT, 1.0f, 1.0f);

                                if (debugMode) {
                                    logger.info("IDC19三叉戟攻击命中玩家: " + player.getName() + "，伤害: " + tridentDamage);
                                }

                                // 标记为已击中
                                hasHit = true;
                                break;
                            }
                        }

                        // 检测是否碰到方块
                        if (!currentLoc.getBlock().isPassable()) {
                            // 播放击中方块音效
                            currentLoc.getWorld().playSound(currentLoc, Sound.ITEM_TRIDENT_HIT_GROUND, 1.0f, 1.0f);

                            // 标记为已击中
                            hasHit = true;
                        }
                    }
                }.runTaskTimer(plugin, 0, 1); // 立即开始，每tick执行一次
            }
        }
    }

    /**
     * 执行隐身技能
     */
    private void performInvisibilitySkill(org.bukkit.entity.Drowned drowned, int invisibilityDuration) {
        // 播放隐身效果音效和粒子
        drowned.getWorld().playSound(drowned.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.0f);
        drowned.getWorld().spawnParticle(Particle.DRAGON_BREATH, drowned.getLocation().add(0, 1, 0), 30, 0.5, 1, 0.5, 0.05);

        // 添加隐身效果
        drowned.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, invisibilityDuration, 0)); // 3秒隐身

        if (debugMode) {
            logger.info("IDC19变异僵尸Max使用隐身技能，持续时间: " + (invisibilityDuration/20) + "秒");
        }
    }

    /**
     * 执行传送能力
     */
    private void performTeleportSkill(org.bukkit.entity.Drowned drowned, double teleportRange,
                                     double teleportDistanceMin, double teleportDistanceMax) {
        // 寻找最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : drowned.getNearbyEntities(teleportRange, teleportRange, teleportRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = player.getLocation().distance(drowned.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        // 如果找到目标玩家，传送到其附近
        if (target != null) {
            // 计算随机传送位置（在玩家周围指定范围内）
            Location playerLoc = target.getLocation();

            // 生成随机角度和距离
            double angle = Math.random() * 2 * Math.PI;
            double distance = teleportDistanceMin + Math.random() * (teleportDistanceMax - teleportDistanceMin); // 3-5格之间的随机距离

            // 计算偏移量
            double offsetX = Math.cos(angle) * distance;
            double offsetZ = Math.sin(angle) * distance;

            // 创建新的传送位置
            Location teleportLoc = playerLoc.clone().add(offsetX, 0, offsetZ);

            // 确保传送位置是安全的（不在方块内）
            teleportLoc.setY(playerLoc.getY());
            while (!teleportLoc.getBlock().getType().isAir() && teleportLoc.getY() < 255) {
                teleportLoc.setY(teleportLoc.getY() + 1);
            }

            // 如果找不到安全位置，尝试向下寻找
            if (!teleportLoc.getBlock().getType().isAir()) {
                teleportLoc.setY(playerLoc.getY());
                while (!teleportLoc.getBlock().getType().isAir() && teleportLoc.getY() > 0) {
                    teleportLoc.setY(teleportLoc.getY() - 1);
                }
            }

            // 如果找到安全位置，执行传送
            if (teleportLoc.getBlock().getType().isAir() && teleportLoc.clone().add(0, 1, 0).getBlock().getType().isAir()) {
                // 播放传送前效果
                drowned.getWorld().playSound(drowned.getLocation(), Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.8f);
                drowned.getWorld().spawnParticle(Particle.PORTAL, drowned.getLocation().add(0, 1, 0), 30, 0.5, 1, 0.5, 0.05);

                // 执行传送
                drowned.teleport(teleportLoc);

                // 播放传送后效果
                drowned.getWorld().playSound(teleportLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.2f);
                drowned.getWorld().spawnParticle(Particle.PORTAL, teleportLoc.add(0, 1, 0), 30, 0.5, 1, 0.5, 0.05);

                if (debugMode) {
                    logger.info("IDC19变异僵尸Max传送到玩家附近: " + target.getName() + "，距离: " + String.format("%.1f", distance));
                }
            }
        }
    }

    /**
     * 生成IDC20灵魂坚守者（监守者形态或铁傀儡形态）
     */
    private LivingEntity spawnSoulGuardian(Location location, EntityOverrideConfig config) {
        try {
            LivingEntity soulGuardian = null;

            // 尝试生成监守者实体（Minecraft 1.19+）
            try {
                if (debugMode) {
                    logger.info("尝试生成监守者实体（WARDEN）");
                }
                soulGuardian = (LivingEntity) location.getWorld().spawnEntity(location, EntityType.valueOf("WARDEN"));
                if (debugMode) {
                    logger.info("监守者实体生成成功");
                }
            } catch (IllegalArgumentException e) {
                // 如果服务器不支持WARDEN实体类型，则使用铁傀儡作为后备
                if (debugMode) {
                    logger.warning("服务器不支持WARDEN实体类型（需要Minecraft 1.19+），将使用铁傀儡作为替代");
                }
                soulGuardian = (LivingEntity) location.getWorld().spawnEntity(location, EntityType.IRON_GOLEM);
                if (debugMode) {
                    logger.info("铁傀儡实体（作为WARDEN替代）生成成功");
                }
            } catch (Exception e) {
                logger.severe("生成实体时出现异常: " + e.getMessage());
                e.printStackTrace();
                return null;
            }

            if (soulGuardian == null) {
                logger.severe("灵魂坚守者生成失败，返回了null");
                return null;
            }

            // 设置基础属性
            if (config.healthOverride > 0) {
                soulGuardian.setMaxHealth(config.healthOverride);
                soulGuardian.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                soulGuardian.setCustomName(config.customNameOverride);
                soulGuardian.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度II + 额外生命30级 + 伤害吸收30级）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            soulGuardian.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC20添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认效果
                soulGuardian.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 1)); // 速度II
                soulGuardian.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 29)); // 额外生命30级
                soulGuardian.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, Integer.MAX_VALUE, 29)); // 伤害吸收30级
            }

            // 启用敌对AI（如果配置启用）
            boolean hostileAiEnabled = true;
            if (config.specialAbilities.containsKey("hostile_ai_enabled")) {
                hostileAiEnabled = (Boolean) config.specialAbilities.get("hostile_ai_enabled");
            }

            if (hostileAiEnabled) {
                // 启用敌对AI，确保主动攻击玩家而不是其他生物
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getHostileAIManager() != null) {
                        dzPlugin.getHostileAIManager().enableHostileAI(soulGuardian);
                        if (debugMode) {
                            logger.info("已为IDC20灵魂坚守者启用敌对AI");
                        }
                    }
                }
            }

            // 启用灵魂坚守者的技能
            enableSoulGuardianSkills(soulGuardian, config);

            // 设置元数据标记
            soulGuardian.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            soulGuardian.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            soulGuardian.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            soulGuardian.setMetadata("entityId", new FixedMetadataValue(plugin, "idc20"));
            soulGuardian.setMetadata("soulGuardian", new FixedMetadataValue(plugin, true));

            if (debugMode) {
                logger.info("成功生成IDC20灵魂坚守者，生命值: " + soulGuardian.getHealth() + "/" + soulGuardian.getMaxHealth() +
                           "，名称: " + soulGuardian.getCustomName() + "，实体类型: " + soulGuardian.getType().name());
            }

            return soulGuardian;

        } catch (Exception e) {
            logger.severe("生成IDC20灵魂坚守者时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用灵魂坚守者的技能（四重技能系统）
     */
    private void enableSoulGuardianSkills(LivingEntity soulGuardian, EntityOverrideConfig config) {
        // 启用主动追踪玩家
        boolean playerTrackingEnabled = true;
        if (config.specialAbilities.containsKey("player_tracking_enabled")) {
            playerTrackingEnabled = (Boolean) config.specialAbilities.get("player_tracking_enabled");
        }

        // 启用声波弹攻击
        boolean sonicAttackEnabled = true;
        if (config.specialAbilities.containsKey("sonic_attack_enabled")) {
            sonicAttackEnabled = (Boolean) config.specialAbilities.get("sonic_attack_enabled");
        }

        // 启用黑曜石方块攻击
        boolean obsidianAttackEnabled = true;
        if (config.specialAbilities.containsKey("obsidian_attack_enabled")) {
            obsidianAttackEnabled = (Boolean) config.specialAbilities.get("obsidian_attack_enabled");
        }

        // 启用击飞攻击
        boolean knockupAttackEnabled = true;
        if (config.specialAbilities.containsKey("knockup_attack_enabled")) {
            knockupAttackEnabled = (Boolean) config.specialAbilities.get("knockup_attack_enabled");
        }

        if (playerTrackingEnabled || sonicAttackEnabled || obsidianAttackEnabled || knockupAttackEnabled) {
            startSoulGuardianAllSkills(soulGuardian, config, playerTrackingEnabled, sonicAttackEnabled,
                                     obsidianAttackEnabled, knockupAttackEnabled);
        }

        if (debugMode) {
            logger.info("IDC20灵魂坚守者技能启用状态 - 主动追踪: " + playerTrackingEnabled +
                       ", 声波攻击: " + sonicAttackEnabled + ", 黑曜石攻击: " + obsidianAttackEnabled +
                       ", 击飞攻击: " + knockupAttackEnabled);
        }
    }

    /**
     * 启动灵魂坚守者的所有技能（四重技能系统）
     */
    private void startSoulGuardianAllSkills(LivingEntity soulGuardian, EntityOverrideConfig config,
                                           final boolean playerTrackingEnabled, final boolean sonicAttackEnabled,
                                           final boolean obsidianAttackEnabled, final boolean knockupAttackEnabled) {

        // 从配置中获取技能参数
        final int trackingInterval = config.skillCooldownOverrides.getOrDefault("tracking_interval", 5); // 默认每0.25秒
        final int sonicAttackInterval = config.skillCooldownOverrides.getOrDefault("sonic_attack_interval", 60); // 默认每3秒
        final int obsidianAttackInterval = config.skillCooldownOverrides.getOrDefault("obsidian_attack_interval", 40); // 默认每2秒
        final int knockupAttackInterval = config.skillCooldownOverrides.getOrDefault("knockup_attack_interval", 60); // 默认每3秒

        final double trackingRange = config.skillCooldownOverrides.getOrDefault("tracking_range", 30).doubleValue(); // 默认30格
        final double sonicRange = config.skillCooldownOverrides.getOrDefault("sonic_range", 30).doubleValue(); // 默认30格
        final double obsidianRange = config.skillCooldownOverrides.getOrDefault("obsidian_range", 30).doubleValue(); // 默认30格
        final double knockupRange = config.skillCooldownOverrides.getOrDefault("knockup_range", 15).doubleValue(); // 默认15格
        final double meleeRange = config.skillCooldownOverrides.getOrDefault("melee_range", 4).doubleValue(); // 默认4格

        final double meleeDamage = config.skillCooldownOverrides.getOrDefault("melee_damage", 15).doubleValue(); // 默认15点
        final double sonicDamage = config.skillCooldownOverrides.getOrDefault("sonic_damage", 20).doubleValue(); // 默认20点
        final double obsidianDamage = config.skillCooldownOverrides.getOrDefault("obsidian_damage", 25).doubleValue(); // 默认25点
        final double knockupStrength = config.skillCooldownOverrides.getOrDefault("knockup_strength", 5).doubleValue(); // 默认5格

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：主动追踪玩家
        if (playerTrackingEnabled) {
            BukkitTask trackingTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (soulGuardian == null || soulGuardian.isDead() || !soulGuardian.isValid()) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(soulGuardian);
                        return;
                    }

                    performSoulGuardianPlayerTracking(soulGuardian, trackingRange, meleeRange, meleeDamage);
                }
            }.runTaskTimer(plugin, 5, trackingInterval); // 0.25秒后开始，按配置间隔执行

            tasks.add(trackingTask);
        }

        // 任务2：声波弹攻击
        if (sonicAttackEnabled) {
            BukkitTask sonicTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (soulGuardian == null || soulGuardian.isDead() || !soulGuardian.isValid()) {
                        this.cancel();
                        return;
                    }

                    performSoulGuardianSonicAttack(soulGuardian, sonicRange, sonicDamage);
                }
            }.runTaskTimer(plugin, 40, sonicAttackInterval); // 2秒后开始，按配置间隔执行

            tasks.add(sonicTask);
        }

        // 任务3：黑曜石方块攻击
        if (obsidianAttackEnabled) {
            BukkitTask obsidianTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (soulGuardian == null || soulGuardian.isDead() || !soulGuardian.isValid()) {
                        this.cancel();
                        return;
                    }

                    performSoulGuardianObsidianAttack(soulGuardian, obsidianRange, obsidianDamage);
                }
            }.runTaskTimer(plugin, 40, obsidianAttackInterval); // 2秒后开始，按配置间隔执行

            tasks.add(obsidianTask);
        }

        // 任务4：向上击飞+黑曜石柱攻击
        if (knockupAttackEnabled) {
            BukkitTask knockupTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在
                    if (soulGuardian == null || soulGuardian.isDead() || !soulGuardian.isValid()) {
                        this.cancel();
                        return;
                    }

                    performSoulGuardianKnockupAttack(soulGuardian, knockupRange, knockupStrength);
                }
            }.runTaskTimer(plugin, 60, knockupAttackInterval); // 3秒后开始，按配置间隔执行

            tasks.add(knockupTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(soulGuardian, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC20灵魂坚守者技能任务已启动 - 追踪: " + playerTrackingEnabled +
                       ", 声波攻击: " + sonicAttackEnabled + ", 黑曜石攻击: " + obsidianAttackEnabled +
                       ", 击飞攻击: " + knockupAttackEnabled);
        }
    }

    /**
     * 执行灵魂坚守者主动追踪玩家
     */
    private void performSoulGuardianPlayerTracking(LivingEntity soulGuardian, double trackingRange,
                                                  double meleeRange, double meleeDamage) {
        Location location = soulGuardian.getLocation();

        // 查找追踪范围内最近的玩家
        Player nearestPlayer = null;
        double minDistance = trackingRange;

        for (Entity entity : location.getWorld().getNearbyEntities(location, trackingRange, trackingRange, trackingRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestPlayer = player;
                    }
                }
            }
        }

        if (nearestPlayer != null) {
            // 计算方向向量
            Vector direction = nearestPlayer.getLocation().toVector().subtract(location.toVector()).normalize();

            // 让灵魂坚守者面向玩家
            Location lookLocation = location.clone();
            lookLocation.setDirection(direction);
            soulGuardian.teleport(lookLocation);

            // 设置灵魂坚守者的路径目标 - 更积极地追踪玩家
            if (soulGuardian instanceof org.bukkit.entity.Mob) {
                ((org.bukkit.entity.Mob) soulGuardian).getPathfinder().moveTo(nearestPlayer, 1.5);
            }

            // 如果距离较近，直接攻击
            if (minDistance <= meleeRange) {
                // 近距离攻击玩家
                nearestPlayer.damage(meleeDamage, soulGuardian);

                // 击飞效果
                Vector knockback = direction.clone().multiply(2.0).setY(0.8);
                nearestPlayer.setVelocity(knockback);

                // 播放攻击音效
                soulGuardian.getWorld().playSound(soulGuardian.getLocation(), Sound.ENTITY_IRON_GOLEM_ATTACK, 1.0f, 0.8f);

                if (debugMode) {
                    logger.info("IDC20近战攻击玩家: " + nearestPlayer.getName() + "，伤害: " + meleeDamage);
                }
            }

            if (debugMode && minDistance > meleeRange) {
                logger.info("IDC20追踪玩家: " + nearestPlayer.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 执行灵魂坚守者声波弹攻击（更大范围，更高频率）
     */
    private void performSoulGuardianSonicAttack(LivingEntity soulGuardian, double sonicRange, double sonicDamage) {
        Location location = soulGuardian.getLocation();

        // 查找声波攻击范围内最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : location.getWorld().getNearbyEntities(location, sonicRange, sonicRange, sonicRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 播放声波攻击音效
            soulGuardian.getWorld().playSound(soulGuardian.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.5f);

            // 计算方向向量
            Vector direction = target.getEyeLocation().toVector()
                    .subtract(soulGuardian.getLocation().add(0, 1.0, 0).toVector())
                    .normalize();

            // 发射声波弹（使用粒子轨迹模拟）
            new BukkitRunnable() {
                private Location currentLoc = soulGuardian.getLocation().add(0, 1.0, 0);
                private int steps = 0;
                private final int maxSteps = 60; // 更大射程
                private final double speed = 1.0; // 飞行速度

                @Override
                public void run() {
                    if (steps >= maxSteps) {
                        this.cancel();
                        return;
                    }

                    // 移动声波弹位置
                    currentLoc.add(direction.clone().multiply(speed));

                    // 显示声波粒子轨迹（更大范围的粒子效果）
                    currentLoc.getWorld().spawnParticle(Particle.SONIC_BOOM, currentLoc, 2, 0.3, 0.3, 0.3, 0);
                    currentLoc.getWorld().spawnParticle(Particle.SWEEP_ATTACK, currentLoc, 5, 0.5, 0.5, 0.5, 0.1);
                    currentLoc.getWorld().spawnParticle(Particle.SOUL_FIRE_FLAME, currentLoc, 3, 0.2, 0.2, 0.2, 0.05);

                    // 检测碰撞（更大范围）
                    for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.5, 1.5, 1.5)) {
                        if (entity instanceof Player && entity != soulGuardian) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害
                            hitPlayer.damage(sonicDamage, soulGuardian);

                            // 更强的击飞效果
                            Vector knockback = direction.clone().multiply(2.5).setY(1.0);
                            hitPlayer.setVelocity(knockback);

                            // 播放命中音效
                            hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.6f);

                            // 更大的爆炸效果
                            currentLoc.getWorld().spawnParticle(Particle.EXPLOSION, currentLoc, 8, 0.8, 0.8, 0.8, 0.2);

                            if (debugMode) {
                                logger.info("IDC20声波弹命中玩家: " + hitPlayer.getName() + "，伤害: " + sonicDamage);
                            }

                            this.cancel();
                            return;
                        }
                    }

                    // 检测方块碰撞
                    Block block = currentLoc.getBlock();
                    if (block.getType().isSolid()) {
                        // 命中方块，创建更大的爆炸效果
                        currentLoc.getWorld().spawnParticle(Particle.EXPLOSION, currentLoc, 10, 1.0, 1.0, 1.0, 0.3);
                        currentLoc.getWorld().playSound(currentLoc, Sound.ENTITY_GENERIC_EXPLODE, 1.0f, 0.8f);
                        this.cancel();
                        return;
                    }

                    steps++;
                }
            }.runTaskTimer(plugin, 0, 1); // 每tick执行一次

            if (debugMode) {
                logger.info("IDC20发射声波弹，目标: " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 执行灵魂坚守者黑曜石方块攻击（频率加倍，更快收回）
     */
    private void performSoulGuardianObsidianAttack(LivingEntity soulGuardian, double obsidianRange, double obsidianDamage) {
        Location location = soulGuardian.getLocation();

        // 查找黑曜石攻击范围内最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : location.getWorld().getNearbyEntities(location, obsidianRange, obsidianRange, obsidianRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 播放黑曜石方块攻击音效
            soulGuardian.getWorld().playSound(soulGuardian.getLocation(), Sound.BLOCK_STONE_BREAK, 1.0f, 0.5f);

            // 发射真实黑曜石方块延伸攻击
            shootRealObsidianBlocks(soulGuardian, target, obsidianDamage);

            if (debugMode) {
                logger.info("IDC20发射黑曜石方块攻击，目标: " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 发射真实黑曜石方块延伸攻击（与原版一致，但收回更快）
     */
    private void shootRealObsidianBlocks(LivingEntity soulGuardian, Player target, double obsidianDamage) {
        final Location start = soulGuardian.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建黑曜石方块攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final int maxDistance = 25; // 更大射程
            private final List<Location> blockLocations = new ArrayList<>();

            @Override
            public void run() {
                if (soulGuardian == null || !soulGuardian.isValid() || soulGuardian.isDead() || distance >= maxDistance) {
                    // 延伸完成，0.5秒后移除所有黑曜石方块（更快收回）
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.SMOKE, loc, 15, 0.5, 0.5, 0.5, 0.1);
                                    world.spawnParticle(Particle.SOUL_FIRE_FLAME, loc, 5, 0.3, 0.3, 0.3, 0.05);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块（更快）

                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.OBSIDIAN) {
                    // 如果碰到墙壁，停止延伸
                    this.cancel();

                    // 播放碰撞音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 移除所有黑曜石方块 - 延伸完成0.5秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                Block block = loc.getBlock();
                                if (block.getType() == Material.OBSIDIAN) {
                                    block.setType(Material.AIR);
                                    world.spawnParticle(Particle.SMOKE, loc, 15, 0.5, 0.5, 0.5, 0.1);
                                    world.spawnParticle(Particle.SOUL_FIRE_FLAME, loc, 5, 0.3, 0.3, 0.3, 0.05);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块

                    return;
                }

                // 在当前位置创建黑曜石方块
                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.OBSIDIAN);
                    blockLocations.add(currentLoc.clone());

                    // 播放黑曜石方块生成音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_PLACE, 0.8f, 0.6f);

                    // 生成黑曜石粒子效果
                    world.spawnParticle(Particle.SMOKE, currentLoc, 10, 0.3, 0.3, 0.3, 0.05);

                    // 检测是否击中玩家
                    for (Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != soulGuardian) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害（更高伤害）
                            hitPlayer.damage(obsidianDamage, soulGuardian);

                            // 禁止移动1秒（通过给予缓慢效果实现）
                            hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20, 100)); // 100级缓慢

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                            if (debugMode) {
                                logger.info("IDC20黑曜石攻击命中玩家: " + hitPlayer.getName() + "，伤害: " + obsidianDamage + "，缓慢1秒");
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（最高速度）
    }

    /**
     * 执行灵魂坚守者向上击飞+黑曜石柱攻击
     */
    private void performSoulGuardianKnockupAttack(LivingEntity soulGuardian, double knockupRange, double knockupStrength) {
        Location location = soulGuardian.getLocation();

        // 查找击飞攻击范围内最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : location.getWorld().getNearbyEntities(location, knockupRange, knockupRange, knockupRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 播放击飞音效
            soulGuardian.getWorld().playSound(soulGuardian.getLocation(), Sound.ENTITY_WARDEN_SONIC_BOOM, 1.0f, 0.8f);
            soulGuardian.getWorld().playSound(soulGuardian.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 1.0f, 0.6f);

            // 向上击飞玩家并生成黑曜石柱
            knockupPlayerWithObsidianPillar(soulGuardian, target, knockupStrength);

            if (debugMode) {
                logger.info("IDC20向上击飞攻击，目标: " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 向上击飞玩家并生成黑曜石柱（直到收回）
     */
    private void knockupPlayerWithObsidianPillar(LivingEntity soulGuardian, Player target, double knockupStrength) {
        final Location playerLoc = target.getLocation();
        final World world = playerLoc.getWorld();

        // 向上击飞玩家
        Vector knockupVector = new Vector(0, knockupStrength, 0); // 直接向上击飞
        target.setVelocity(knockupVector);

        // 造成击飞伤害
        target.damage(20.0, soulGuardian);

        // 播放击飞音效和粒子效果
        world.playSound(playerLoc, Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);
        world.spawnParticle(Particle.EXPLOSION, playerLoc, 10, 1.0, 1.0, 1.0, 0.2);
        world.spawnParticle(Particle.SOUL_FIRE_FLAME, playerLoc, 20, 1.0, 1.0, 1.0, 0.1);

        // 在玩家被击飞的路径上生成黑曜石柱
        final List<Location> pillarBlocks = new ArrayList<>();
        final int maxHeight = (int) (knockupStrength * 2); // 根据击飞强度计算柱子高度

        // 延迟生成黑曜石柱（让玩家先被击飞）
        new BukkitRunnable() {
            private int currentHeight = 0;

            @Override
            public void run() {
                if (currentHeight >= maxHeight) {
                    // 柱子生成完成，1秒后开始移除
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            // 从上到下移除黑曜石柱，创造收回效果
                            new BukkitRunnable() {
                                private int removeHeight = maxHeight - 1;

                                @Override
                                public void run() {
                                    if (removeHeight < 0) {
                                        this.cancel();
                                        return;
                                    }

                                    // 移除当前高度的黑曜石
                                    Location removeLocation = playerLoc.clone().add(0, removeHeight, 0);
                                    Block block = removeLocation.getBlock();
                                    if (block.getType() == Material.OBSIDIAN) {
                                        block.setType(Material.AIR);

                                        // 播放移除音效和粒子效果
                                        world.playSound(removeLocation, Sound.BLOCK_STONE_BREAK, 0.5f, 1.2f);
                                        world.spawnParticle(Particle.SMOKE, removeLocation, 10, 0.3, 0.3, 0.3, 0.05);
                                        world.spawnParticle(Particle.SOUL_FIRE_FLAME, removeLocation, 5, 0.2, 0.2, 0.2, 0.03);
                                    }

                                    removeHeight--;
                                }
                            }.runTaskTimer(plugin, 0L, 2L); // 每2tick移除一层，创造收回动画
                        }
                    }.runTaskLater(plugin, 20L); // 1秒后开始移除

                    this.cancel();
                    return;
                }

                // 在当前高度生成黑曜石
                Location blockLocation = playerLoc.clone().add(0, currentHeight, 0);
                Block block = blockLocation.getBlock();

                if (block.getType() == Material.AIR || block.getType() == Material.CAVE_AIR) {
                    block.setType(Material.OBSIDIAN);
                    pillarBlocks.add(blockLocation.clone());

                    // 播放生成音效和粒子效果
                    world.playSound(blockLocation, Sound.BLOCK_STONE_PLACE, 0.8f, 0.6f);
                    world.spawnParticle(Particle.SMOKE, blockLocation, 8, 0.3, 0.3, 0.3, 0.05);

                    // 检测是否击中玩家（如果玩家在柱子路径上）
                    for (Entity entity : world.getNearbyEntities(blockLocation, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player && entity != soulGuardian) {
                            Player hitPlayer = (Player) entity;

                            // 造成额外伤害
                            hitPlayer.damage(15.0, soulGuardian);

                            // 播放命中音效
                            hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 1.0f);

                            if (debugMode) {
                                logger.info("IDC20黑曜石柱命中玩家: " + hitPlayer.getName() + "，伤害: 15.0");
                            }
                        }
                    }
                }

                currentHeight++;
            }
        }.runTaskTimer(plugin, 10L, 3L); // 0.5秒后开始，每3tick生成一层

        if (debugMode) {
            logger.info("IDC20向上击飞玩家: " + target.getName() + "，击飞强度: " + knockupStrength + "，柱子高度: " + maxHeight);
        }
    }

    /**
     * 生成IDC21凋零领主（凋零形态）
     */
    private LivingEntity spawnWitherLord(Location location, EntityOverrideConfig config) {
        try {
            // 生成凋零实体
            org.bukkit.entity.Wither witherLord = (org.bukkit.entity.Wither) location.getWorld().spawnEntity(location, EntityType.WITHER);

            // 设置基础属性
            if (config.healthOverride > 0) {
                witherLord.setMaxHealth(config.healthOverride);
                witherLord.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                witherLord.setCustomName(config.customNameOverride);
                witherLord.setCustomNameVisible(true); // 凋零领主名称始终可见
            }

            // 防止实体消失
            witherLord.setRemoveWhenFarAway(false);

            // 启用凋零领主的技能
            enableWitherLordSkills(witherLord, config);

            // 设置元数据标记
            witherLord.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            witherLord.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            witherLord.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            witherLord.setMetadata("entityId", new FixedMetadataValue(plugin, "idc21"));
            witherLord.setMetadata("witherLord", new FixedMetadataValue(plugin, true));

            // 播放生成音效
            location.getWorld().playSound(location, Sound.ENTITY_WITHER_SPAWN, 1.0f, 0.5f);
            location.getWorld().playSound(location, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.5f);

            // 显示生成粒子效果
            location.getWorld().spawnParticle(Particle.SOUL, location, 50, 1.0, 1.0, 1.0, 0.1);
            location.getWorld().spawnParticle(Particle.SOUL_FIRE_FLAME, location, 50, 1.0, 1.0, 1.0, 0.1);
            location.getWorld().spawnParticle(Particle.DRAGON_BREATH, location, 50, 1.0, 1.0, 1.0, 0.1);

            if (debugMode) {
                logger.info("成功生成IDC21凋零领主，生命值: " + witherLord.getHealth() + "/" + witherLord.getMaxHealth() +
                           "，名称: " + witherLord.getCustomName());
            }

            return witherLord;

        } catch (Exception e) {
            logger.severe("生成IDC21凋零领主时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用凋零领主的技能（三重技能系统）
     */
    private void enableWitherLordSkills(org.bukkit.entity.Wither witherLord, EntityOverrideConfig config) {
        // 启用黑曜石方块攻击
        boolean obsidianAttackEnabled = true;
        if (config.specialAbilities.containsKey("obsidian_attack_enabled")) {
            obsidianAttackEnabled = (Boolean) config.specialAbilities.get("obsidian_attack_enabled");
        }

        // 启用凋零头颅攻击
        boolean skullAttackEnabled = true;
        if (config.specialAbilities.containsKey("skull_attack_enabled")) {
            skullAttackEnabled = (Boolean) config.specialAbilities.get("skull_attack_enabled");
        }

        // 启用下界栅栏攻击
        boolean fenceAttackEnabled = true;
        if (config.specialAbilities.containsKey("fence_attack_enabled")) {
            fenceAttackEnabled = (Boolean) config.specialAbilities.get("fence_attack_enabled");
        }

        if (obsidianAttackEnabled || skullAttackEnabled || fenceAttackEnabled) {
            startWitherLordAllSkills(witherLord, config, obsidianAttackEnabled, skullAttackEnabled, fenceAttackEnabled);
        }

        if (debugMode) {
            logger.info("IDC21凋零领主技能启用状态 - 黑曜石攻击: " + obsidianAttackEnabled +
                       ", 凋零头颅攻击: " + skullAttackEnabled + ", 下界栅栏攻击: " + fenceAttackEnabled);
        }
    }

    /**
     * 启动凋零领主的所有技能（三重技能系统）
     */
    private void startWitherLordAllSkills(org.bukkit.entity.Wither witherLord, EntityOverrideConfig config,
                                         final boolean obsidianAttackEnabled, final boolean skullAttackEnabled,
                                         final boolean fenceAttackEnabled) {

        // 从配置中获取技能参数
        final int obsidianAttackInterval = config.skillCooldownOverrides.getOrDefault("obsidian_attack_interval", 80); // 默认4秒
        final int skullAttackInterval = config.skillCooldownOverrides.getOrDefault("skull_attack_interval", 30); // 默认1.5秒
        final int fenceAttackInterval = config.skillCooldownOverrides.getOrDefault("fence_attack_interval", 100); // 默认5秒

        final double attackRange = config.skillCooldownOverrides.getOrDefault("attack_range", 30).doubleValue(); // 默认30格
        final double obsidianDamage = config.skillCooldownOverrides.getOrDefault("obsidian_damage", 10).doubleValue(); // 默认10点
        final double fenceDamage = config.skillCooldownOverrides.getOrDefault("fence_damage", 5).doubleValue(); // 默认5点
        final double skullSpeedMultiplier = config.skillCooldownOverrides.getOrDefault("skull_speed_multiplier", 2).doubleValue(); // 默认2倍
        final int fenceSlownessLevel = config.skillCooldownOverrides.getOrDefault("fence_slowness_level", 2); // 默认减速3
        final int fenceSlownessDuration = config.skillCooldownOverrides.getOrDefault("fence_slowness_duration", 60); // 默认3秒
        final int obsidianMaxDistance = config.skillCooldownOverrides.getOrDefault("obsidian_max_distance", 25); // 默认25格
        final int fenceRayLength = config.skillCooldownOverrides.getOrDefault("fence_ray_length", 15); // 默认15格
        final int fenceRestoreDelay = config.skillCooldownOverrides.getOrDefault("fence_restore_delay", 40); // 默认2秒

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 任务1：黑曜石方块攻击
        if (obsidianAttackEnabled) {
            BukkitTask obsidianTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在或真正死亡
                    if (witherLord == null || !witherLord.isValid() || isWitherTrulyDead(witherLord)) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(witherLord);
                        return;
                    }

                    performWitherLordObsidianAttack(witherLord, attackRange, obsidianDamage, obsidianMaxDistance);
                }
            }.runTaskTimer(plugin, 40, obsidianAttackInterval); // 2秒后开始，按配置间隔执行

            tasks.add(obsidianTask);
        }

        // 任务2：高速凋零头颅攻击
        if (skullAttackEnabled) {
            BukkitTask skullTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在或真正死亡
                    if (witherLord == null || !witherLord.isValid() || isWitherTrulyDead(witherLord)) {
                        this.cancel();
                        return;
                    }

                    performWitherLordSkullAttack(witherLord, attackRange, skullSpeedMultiplier);
                }
            }.runTaskTimer(plugin, 20, skullAttackInterval); // 1秒后开始，按配置间隔执行

            tasks.add(skullTask);
        }

        // 任务3：下界栅栏攻击
        if (fenceAttackEnabled) {
            BukkitTask fenceTask = new BukkitRunnable() {
                @Override
                public void run() {
                    // 检查实体是否还存在或真正死亡
                    if (witherLord == null || !witherLord.isValid() || isWitherTrulyDead(witherLord)) {
                        this.cancel();
                        return;
                    }

                    performWitherLordFenceAttack(witherLord, attackRange, fenceDamage, fenceSlownessLevel,
                                                fenceSlownessDuration, fenceRayLength, fenceRestoreDelay);
                }
            }.runTaskTimer(plugin, 60, fenceAttackInterval); // 3秒后开始，按配置间隔执行

            tasks.add(fenceTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(witherLord, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC21凋零领主技能任务已启动 - 黑曜石攻击: " + obsidianAttackEnabled +
                       ", 凋零头颅攻击: " + skullAttackEnabled + ", 下界栅栏攻击: " + fenceAttackEnabled);
        }
    }

    /**
     * 执行凋零领主黑曜石方块攻击（碰撞时四向延伸）
     */
    private void performWitherLordObsidianAttack(org.bukkit.entity.Wither witherLord, double attackRange,
                                                double obsidianDamage, int maxDistance) {
        Location location = witherLord.getLocation();

        // 查找攻击范围内最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = location.distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 播放黑曜石方块攻击音效
            witherLord.getWorld().playSound(witherLord.getLocation(), Sound.BLOCK_STONE_BREAK, 1.0f, 0.5f);

            // 发射黑曜石方块攻击
            shootObsidianBlocksFromWitherLord(witherLord, target, obsidianDamage, maxDistance);

            if (debugMode) {
                logger.info("IDC21发射黑曜石方块攻击，目标: " + target.getName() + "，距离: " + String.format("%.1f", minDistance));
            }
        }
    }

    /**
     * 从凋零领主发射黑曜石方块攻击（碰撞时四向延伸）
     */
    private void shootObsidianBlocksFromWitherLord(org.bukkit.entity.Wither witherLord, Player target,
                                                  double obsidianDamage, int maxDistance) {
        final Location start = witherLord.getLocation().add(0, 1.5, 0);
        final Vector direction = target.getLocation().add(0, 1.0, 0).subtract(start).toVector().normalize();
        final World world = start.getWorld();

        // 创建黑曜石方块攻击任务
        new BukkitRunnable() {
            private final Location currentLoc = start.clone();
            private int distance = 0;
            private final List<Location> blockLocations = new ArrayList<>();
            private final Map<Location, Material> originalBlocks = new HashMap<>(); // 存储原始方块

            @Override
            public void run() {
                if (witherLord == null || isWitherTrulyDead(witherLord) || distance > maxDistance) {
                    // 恢复所有方块
                    for (Location loc : blockLocations) {
                        if (originalBlocks.containsKey(loc)) {
                            loc.getBlock().setType(originalBlocks.get(loc));
                        }
                    }
                    this.cancel();
                    return;
                }

                // 移动到下一个位置
                currentLoc.add(direction);
                distance++;

                // 检查当前位置是否是实体方块
                Block block = currentLoc.getBlock();
                if (block.getType().isSolid() && block.getType() != Material.OBSIDIAN) {
                    // 如果碰到墙壁，向四个正交方向延伸
                    Vector[] directions = {
                        new Vector(1, 0, 0),
                        new Vector(-1, 0, 0),
                        new Vector(0, 0, 1),
                        new Vector(0, 0, -1)
                    };

                    // 向每个方向延伸3格
                    for (Vector dir : directions) {
                        for (int i = 1; i <= 3; i++) {
                            Location extendLoc = currentLoc.clone().add(dir.clone().multiply(i));
                            Block extendBlock = extendLoc.getBlock();

                            if (extendBlock.getType() == Material.AIR
                                    || extendBlock.getType() == Material.WATER
                                    || extendBlock.getType() == Material.GRASS_BLOCK) {

                                originalBlocks.put(extendLoc, extendBlock.getType());
                                extendBlock.setType(Material.OBSIDIAN);
                                blockLocations.add(extendLoc);

                                // 显示粒子效果
                                world.spawnParticle(Particle.CRIT, extendLoc, 5, 0.2, 0.2, 0.2, 0.05);
                            }
                        }
                    }

                    // 播放碰撞音效
                    world.playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 移除所有黑曜石方块 - 延伸完成0.5秒后
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : blockLocations) {
                                if (originalBlocks.containsKey(loc)) {
                                    loc.getBlock().setType(originalBlocks.get(loc));
                                    world.spawnParticle(Particle.CRIT, loc, 5, 0.2, 0.2, 0.2, 0.05);
                                }
                            }
                        }
                    }.runTaskLater(plugin, 10L); // 0.5秒后移除黑曜石方块

                    this.cancel();
                    return;
                }

                // 在当前位置创建黑曜石方块
                if (block.getType() == Material.AIR || block.getType() == Material.WATER || block.getType() == Material.GRASS_BLOCK) {
                    originalBlocks.put(currentLoc.clone(), block.getType());
                    block.setType(Material.OBSIDIAN);
                    blockLocations.add(currentLoc.clone());

                    // 播放黑曜石方块生成音效
                    if (distance % 3 == 0) { // 每3格播放一次音效，避免声音过多
                        world.playSound(currentLoc, Sound.BLOCK_STONE_PLACE, 0.3f, 1.0f);
                    }

                    // 检测是否击中玩家
                    for (Entity entity : world.getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害
                            hitPlayer.damage(obsidianDamage, witherLord);

                            // 播放受伤音效
                            hitPlayer.getWorld().playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR, hitPlayer.getLocation().add(0, 1, 0), 15, 0.5, 0.5, 0.5, 0.1);

                            if (debugMode) {
                                logger.info("IDC21黑曜石攻击命中玩家: " + hitPlayer.getName() + "，伤害: " + obsidianDamage);
                            }
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick执行一次（最高速度）
    }

    /**
     * 检查凋零是否真正死亡（修复凋零头颅回血导致的不死bug）
     */
    private boolean isWitherTrulyDead(org.bukkit.entity.Wither wither) {
        // 如果凋零已经被标记为死亡，直接返回true
        if (wither.isDead()) {
            return true;
        }

        // 如果凋零血量为0或接近0，且处于濒死状态超过5秒，强制认为已死亡
        if (wither.getHealth() <= 0.1) {
            // 检查是否已经记录了濒死时间
            String deathTimeKey = "witherDeathTime_" + wither.getUniqueId().toString();
            long currentTime = System.currentTimeMillis();

            if (wither.hasMetadata(deathTimeKey)) {
                long deathTime = wither.getMetadata(deathTimeKey).get(0).asLong();
                if (currentTime - deathTime > 5000) { // 濒死超过5秒
                    // 强制杀死凋零
                    wither.setHealth(0);
                    wither.damage(1000); // 确保死亡

                    if (debugMode) {
                        logger.info("IDC21凋零领主濒死超过5秒，强制死亡以修复不死bug");
                    }

                    return true;
                }
            } else {
                // 第一次检测到濒死状态，记录时间
                wither.setMetadata(deathTimeKey, new FixedMetadataValue(plugin, currentTime));

                if (debugMode) {
                    logger.info("IDC21凋零领主进入濒死状态，开始计时");
                }
            }
        } else {
            // 如果血量恢复了，清除濒死时间记录
            String deathTimeKey = "witherDeathTime_" + wither.getUniqueId().toString();
            if (wither.hasMetadata(deathTimeKey)) {
                wither.removeMetadata(deathTimeKey, plugin);
            }
        }

        return false;
    }

    /**
     * 执行凋零领主高速凋零头颅攻击（蓝色头颅，2倍速度，随机目标）
     */
    private void performWitherLordSkullAttack(org.bukkit.entity.Wither witherLord, double attackRange, double speedMultiplier) {
        Location location = witherLord.getLocation();

        // 查找攻击范围内的所有玩家
        List<Player> targets = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    targets.add(player);
                }
            }
        }

        if (!targets.isEmpty()) {
            // 随机选择一个目标
            Player target = targets.get((int) (Math.random() * targets.size()));

            // 播放凋零头颅发射音效
            witherLord.getWorld().playSound(witherLord.getLocation(), Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.8f);

            // 发射高速蓝色凋零头颅
            shootHighSpeedWitherSkull(witherLord, target, speedMultiplier);

            if (debugMode) {
                logger.info("IDC21发射高速凋零头颅，目标: " + target.getName() + "，速度倍数: " + speedMultiplier);
            }
        }
    }

    /**
     * 发射高速蓝色凋零头颅（使用真实WitherSkull实体，与原版一致）
     */
    private void shootHighSpeedWitherSkull(org.bukkit.entity.Wither witherLord, Player target, double speedMultiplier) {
        // 计算方向向量
        Vector direction = target.getLocation().subtract(witherLord.getLocation()).toVector().normalize();

        // 发射凋零头颅
        Location spawnLoc = witherLord.getLocation().add(0, 1, 0);
        org.bukkit.entity.WitherSkull skull = (org.bukkit.entity.WitherSkull) witherLord.getWorld().spawnEntity(spawnLoc, EntityType.WITHER_SKULL);
        skull.setDirection(direction);
        skull.setCharged(true); // 使用蓝色凋零头颅

        // 设置头颅速度（比普通凋零头颅快）
        skull.setVelocity(direction.multiply(speedMultiplier));

        // 添加元数据，用于识别这是凋零领主的头颅
        skull.setMetadata("witherLordSkull", new FixedMetadataValue(plugin, true));

        // 设置射击者为凋零领主（用于恢复血量功能）
        skull.setShooter(witherLord);

        // 播放发射音效
        witherLord.getWorld().playSound(spawnLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.8f);

        if (debugMode) {
            logger.info("IDC21发射真实蓝色凋零头颅，目标: " + target.getName() + "，速度倍数: " + speedMultiplier);
        }
    }

    /**
     * 执行凋零领主下界栅栏攻击（3条不同角度的射线，5点伤害+减速3）
     */
    private void performWitherLordFenceAttack(org.bukkit.entity.Wither witherLord, double attackRange, double fenceDamage,
                                             int slownessLevel, int slownessDuration, int rayLength, int restoreDelay) {
        Location location = witherLord.getLocation();

        // 查找攻击范围内的所有玩家
        List<Player> targets = new ArrayList<>();
        for (Entity entity : location.getWorld().getNearbyEntities(location, attackRange, attackRange, attackRange)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    targets.add(player);
                }
            }
        }

        if (!targets.isEmpty()) {
            // 随机选择一个目标
            Player target = targets.get((int) (Math.random() * targets.size()));

            // 播放下界栅栏攻击音效
            witherLord.getWorld().playSound(witherLord.getLocation(), Sound.BLOCK_NETHER_BRICKS_BREAK, 1.0f, 0.6f);

            // 发射3条不同角度的下界栅栏射线
            shootNetherFenceRays(witherLord, target, fenceDamage, slownessLevel, slownessDuration, rayLength, restoreDelay);

            if (debugMode) {
                logger.info("IDC21发射下界栅栏攻击，目标: " + target.getName() + "，射线长度: " + rayLength);
            }
        }
    }

    /**
     * 发射3条不同角度的下界栅栏射线（与原版逻辑一致）
     */
    private void shootNetherFenceRays(org.bukkit.entity.Wither witherLord, Player target, double fenceDamage,
                                     int slownessLevel, int slownessDuration, int rayLength, int restoreDelay) {
        final Location witherLoc = witherLord.getLocation();
        final World world = witherLoc.getWorld();
        final Map<Location, Material> originalBlocks = new HashMap<>();
        final List<Location> currentAttackBlocks = new ArrayList<>();

        // 获取附近玩家列表（用于伤害检测）
        List<Player> nearbyPlayers = new ArrayList<>();
        for (Entity entity : witherLord.getNearbyEntities(30, 30, 30)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    nearbyPlayers.add(player);
                }
            }
        }

        // 创建3条不同角度的下界栅栏射线（与原版一致：-30度、0度、+30度）
        for (int angleOffset = -30; angleOffset <= 30; angleOffset += 30) {
            // 计算基础方向向量
            Vector baseDirection = target.getLocation().subtract(witherLoc).toVector().normalize();

            // 应用角度偏移
            double angle = Math.toRadians(angleOffset);
            double x = baseDirection.getX() * Math.cos(angle) - baseDirection.getZ() * Math.sin(angle);
            double z = baseDirection.getX() * Math.sin(angle) + baseDirection.getZ() * Math.cos(angle);
            Vector direction = new Vector(x, baseDirection.getY(), z).normalize();

            // 创建一条下界栅栏射线
            for (int i = 1; i <= rayLength; i++) {
                Location blockLoc = witherLoc.clone().add(direction.clone().multiply(i));

                // 如果方块是空气，替换为下界栅栏
                if (blockLoc.getBlock().getType() == Material.AIR
                        || blockLoc.getBlock().getType() == Material.WATER
                        || blockLoc.getBlock().getType() == Material.GRASS_BLOCK) {
                    originalBlocks.put(blockLoc, blockLoc.getBlock().getType());
                    blockLoc.getBlock().setType(Material.NETHER_BRICK_FENCE);
                    currentAttackBlocks.add(blockLoc);

                    // 显示粒子效果
                    world.spawnParticle(Particle.FLAME, blockLoc, 5, 0.3, 0.3, 0.3, 0.05);

                    // 检查是否有玩家在这个位置
                    for (Player p : nearbyPlayers) {
                        if (p.getLocation().distance(blockLoc) < 1.5) {
                            // 造成伤害并减速
                            p.damage(fenceDamage, witherLord);
                            p.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, slownessDuration, slownessLevel)); // 减速3，持续3秒

                            if (debugMode) {
                                logger.info("IDC21下界栅栏攻击命中玩家: " + p.getName() +
                                           "，伤害: " + fenceDamage + "，减速" + (slownessLevel + 1) + "级");
                            }
                        }
                    }
                }
            }
        }

        // 播放音效
        witherLord.getWorld().playSound(witherLord.getLocation(), Sound.BLOCK_WOOD_PLACE, 1.0f, 0.5f);

        // 安排任务在指定时间后恢复方块
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Location loc : currentAttackBlocks) {
                    if (originalBlocks.containsKey(loc)) {
                        loc.getBlock().setType(originalBlocks.get(loc));
                    }
                }
            }
        }.runTaskLater(plugin, restoreDelay); // 按配置延迟恢复

        if (debugMode) {
            logger.info("IDC21发射下界栅栏攻击，目标: " + target.getName() + "，射线长度: " + rayLength + "，恢复延迟: " + restoreDelay + "tick");
        }
    }

    /**
     * 绕Y轴旋转向量
     */
    private Vector rotateVectorY(Vector vector, double angle) {
        double cos = Math.cos(angle);
        double sin = Math.sin(angle);
        double x = vector.getX() * cos - vector.getZ() * sin;
        double z = vector.getX() * sin + vector.getZ() * cos;
        return new Vector(x, vector.getY(), z);
    }

    /**
     * 生成IDC22异变之王（末影龙形态）
     */
    private LivingEntity spawnMutationKing(Location location, EntityOverrideConfig config) {
        try {
            // 生成末影龙实体
            org.bukkit.entity.EnderDragon mutationKing = (org.bukkit.entity.EnderDragon) location.getWorld().spawnEntity(location, EntityType.ENDER_DRAGON);

            // 设置基础属性
            if (config.healthOverride > 0) {
                mutationKing.setMaxHealth(config.healthOverride);
                mutationKing.setHealth(config.healthOverride);
            }

            if (config.customNameOverride != null && !config.customNameOverride.isEmpty()) {
                mutationKing.setCustomName(config.customNameOverride);
                mutationKing.setCustomNameVisible(false); // 默认隐藏，由EntityNameDisplayListener控制
            }

            // 设置药水效果（速度III）
            if (config.potionEffects != null && !config.potionEffects.isEmpty()) {
                for (Map.Entry<String, EntityOverrideConfig.PotionEffectConfig> effectEntry : config.potionEffects.entrySet()) {
                    try {
                        String effectName = effectEntry.getKey().toUpperCase();
                        EntityOverrideConfig.PotionEffectConfig effectConfig = effectEntry.getValue();

                        PotionEffectType effectType = PotionEffectType.getByName(effectName);
                        if (effectType != null) {
                            int duration = effectConfig.duration == -1 ? Integer.MAX_VALUE : effectConfig.duration;
                            mutationKing.addPotionEffect(new PotionEffect(effectType, duration, effectConfig.level));

                            if (debugMode) {
                                logger.info("为IDC22添加药水效果: " + effectName + " 等级" + (effectConfig.level + 1) +
                                           " 持续时间" + (effectConfig.duration == -1 ? "永久" : effectConfig.duration + "tick"));
                            }
                        }
                    } catch (Exception e) {
                        logger.warning("应用药水效果失败: " + effectEntry.getKey());
                    }
                }
            } else {
                // 默认效果
                mutationKing.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE, 2)); // 速度III
            }

            // 启用敌对AI（如果配置启用）
            boolean hostileAiEnabled = true;
            if (config.specialAbilities.containsKey("hostile_ai_enabled")) {
                hostileAiEnabled = (Boolean) config.specialAbilities.get("hostile_ai_enabled");
            }

            if (hostileAiEnabled) {
                // 启用敌对AI，确保主动攻击玩家而不是其他生物
                if (plugin instanceof org.Ver_zhzh.deathZombieV4.DeathZombieV4) {
                    org.Ver_zhzh.deathZombieV4.DeathZombieV4 dzPlugin = (org.Ver_zhzh.deathZombieV4.DeathZombieV4) plugin;
                    if (dzPlugin.getHostileAIManager() != null) {
                        dzPlugin.getHostileAIManager().enableHostileAI(mutationKing);
                        if (debugMode) {
                            logger.info("已为IDC22异变之王启用敌对AI");
                        }
                    }
                }
            }

            // 启用异变之王的技能
            enableMutationKingSkills(mutationKing, config);

            // 设置元数据标记
            mutationKing.setMetadata("userCustomEntity", new FixedMetadataValue(plugin, true));
            mutationKing.setMetadata("idcZombieEntity", new FixedMetadataValue(plugin, true));
            mutationKing.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            mutationKing.setMetadata("entityId", new FixedMetadataValue(plugin, "idc22"));
            mutationKing.setMetadata("mutationKing", new FixedMetadataValue(plugin, true));

            // 播放生成音效和粒子效果
            location.getWorld().playSound(location, Sound.ENTITY_ENDER_DRAGON_GROWL, 3.0f, 0.5f);
            location.getWorld().playSound(location, Sound.ENTITY_ENDER_DRAGON_AMBIENT, 3.0f, 0.5f);

            // 显示生成粒子效果
            location.getWorld().spawnParticle(Particle.DRAGON_BREATH, location, 100, 3.0, 3.0, 3.0, 0.1);
            location.getWorld().spawnParticle(Particle.END_ROD, location, 100, 3.0, 3.0, 3.0, 0.1);
            location.getWorld().spawnParticle(Particle.PORTAL, location, 100, 3.0, 3.0, 3.0, 0.1);

            if (debugMode) {
                logger.info("成功生成IDC22异变之王，生命值: " + mutationKing.getHealth() + "/" + mutationKing.getMaxHealth() +
                           "，名称: " + mutationKing.getCustomName());
            }

            return mutationKing;

        } catch (Exception e) {
            logger.severe("生成IDC22异变之王时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 启用异变之王的技能（八重技能系统）
     */
    private void enableMutationKingSkills(org.bukkit.entity.EnderDragon mutationKing, EntityOverrideConfig config) {
        // 启用智能移动AI
        boolean movementAiEnabled = true;
        if (config.specialAbilities.containsKey("movement_ai_enabled")) {
            movementAiEnabled = (Boolean) config.specialAbilities.get("movement_ai_enabled");
        }

        // 启用近战攻击
        boolean meleeAttackEnabled = true;
        if (config.specialAbilities.containsKey("melee_attack_enabled")) {
            meleeAttackEnabled = (Boolean) config.specialAbilities.get("melee_attack_enabled");
        }

        // 启用末影水晶攻击
        boolean crystalAttackEnabled = true;
        if (config.specialAbilities.containsKey("crystal_attack_enabled")) {
            crystalAttackEnabled = (Boolean) config.specialAbilities.get("crystal_attack_enabled");
        }

        // 启用龙息攻击
        boolean breathAttackEnabled = true;
        if (config.specialAbilities.containsKey("breath_attack_enabled")) {
            breathAttackEnabled = (Boolean) config.specialAbilities.get("breath_attack_enabled");
        }

        // 启用黑曜石柱攻击
        boolean obsidianAttackEnabled = true;
        if (config.specialAbilities.containsKey("obsidian_attack_enabled")) {
            obsidianAttackEnabled = (Boolean) config.specialAbilities.get("obsidian_attack_enabled");
        }

        // 启用下界栅栏攻击
        boolean fenceAttackEnabled = true;
        if (config.specialAbilities.containsKey("fence_attack_enabled")) {
            fenceAttackEnabled = (Boolean) config.specialAbilities.get("fence_attack_enabled");
        }

        // 启用末影粒子减速场
        boolean enderFieldEnabled = true;
        if (config.specialAbilities.containsKey("ender_field_enabled")) {
            enderFieldEnabled = (Boolean) config.specialAbilities.get("ender_field_enabled");
        }

        // 启用召唤变异生物
        boolean summonEnabled = true;
        if (config.specialAbilities.containsKey("summon_enabled")) {
            summonEnabled = (Boolean) config.specialAbilities.get("summon_enabled");
        }

        // 禁用Boss血条（由原版MutationKing处理，避免重复）
        boolean bossBarEnabled = false;
        if (config.specialAbilities.containsKey("boss_bar_enabled")) {
            bossBarEnabled = (Boolean) config.specialAbilities.get("boss_bar_enabled");
        }

        if (movementAiEnabled || meleeAttackEnabled || crystalAttackEnabled || breathAttackEnabled ||
            obsidianAttackEnabled || fenceAttackEnabled || enderFieldEnabled || summonEnabled) {
            startMutationKingAllSkills(mutationKing, config, movementAiEnabled, meleeAttackEnabled,
                                     crystalAttackEnabled, breathAttackEnabled, obsidianAttackEnabled,
                                     fenceAttackEnabled, enderFieldEnabled, summonEnabled, bossBarEnabled);
        }

        if (debugMode) {
            logger.info("IDC22异变之王技能启用状态 - 移动AI: " + movementAiEnabled +
                       ", 近战攻击: " + meleeAttackEnabled + ", 水晶攻击: " + crystalAttackEnabled +
                       ", 龙息攻击: " + breathAttackEnabled + ", 黑曜石攻击: " + obsidianAttackEnabled +
                       ", 栅栏攻击: " + fenceAttackEnabled + ", 末影场: " + enderFieldEnabled +
                       ", 召唤: " + summonEnabled + ", Boss血条: " + bossBarEnabled);
        }
    }

    /**
     * 启动异变之王的所有技能（八重技能系统）
     */
    private void startMutationKingAllSkills(org.bukkit.entity.EnderDragon mutationKing, EntityOverrideConfig config,
                                           final boolean movementAiEnabled, final boolean meleeAttackEnabled,
                                           final boolean crystalAttackEnabled, final boolean breathAttackEnabled,
                                           final boolean obsidianAttackEnabled, final boolean fenceAttackEnabled,
                                           final boolean enderFieldEnabled, final boolean summonEnabled,
                                           final boolean bossBarEnabled) {

        // 从配置中获取技能参数（修复类型转换问题）
        final int movementInterval = config.skillCooldownOverrides.getOrDefault("movement_interval", 5); // 默认0.25秒
        final int attackInterval = config.skillCooldownOverrides.getOrDefault("attack_interval", 10); // 默认0.5秒
        final long crystalCooldown = config.skillCooldownOverrides.getOrDefault("crystal_attack_cooldown", 15000).longValue(); // 默认15秒
        final long breathCooldown = config.skillCooldownOverrides.getOrDefault("breath_attack_cooldown", 10000).longValue(); // 默认10秒
        final long enderFieldCooldown = config.skillCooldownOverrides.getOrDefault("ender_field_cooldown", 20000).longValue(); // 默认20秒
        final long summonCooldown = config.skillCooldownOverrides.getOrDefault("summon_cooldown", 4000).longValue(); // 默认4秒
        final long obsidianCooldown = config.skillCooldownOverrides.getOrDefault("obsidian_attack_cooldown", 5000).longValue(); // 默认5秒
        final long fenceCooldown = config.skillCooldownOverrides.getOrDefault("nether_fence_cooldown", 6000).longValue(); // 默认6秒
        // 添加黑曜石块攻击独立冷却配置
        final long obsidianBlocksCooldown = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_cooldown", 8000).longValue(); // 默认8秒

        final double meleeDamage = config.skillCooldownOverrides.getOrDefault("melee_damage", 20).doubleValue(); // 默认20点
        final double crystalDamage = config.skillCooldownOverrides.getOrDefault("crystal_damage", 15).doubleValue(); // 默认15点
        final double breathDamage = config.skillCooldownOverrides.getOrDefault("breath_damage", 12).doubleValue(); // 默认12点
        final double obsidianDamage = config.skillCooldownOverrides.getOrDefault("obsidian_damage", 18).doubleValue(); // 默认18点
        final double fenceDamage = config.skillCooldownOverrides.getOrDefault("fence_damage", 10).doubleValue(); // 默认10点
        final double enderFieldRadius = config.skillCooldownOverrides.getOrDefault("ender_field_radius", 10).doubleValue(); // 默认10格
        final int enderFieldDuration = config.skillCooldownOverrides.getOrDefault("ender_field_duration", 200); // 默认10秒

        // 创建任务列表
        List<BukkitTask> tasks = new ArrayList<>();

        // 检查是否已经存在Boss血条（避免重复创建）
        BossBar bossBar = null;
        if (bossBarEnabled) {
            // 检查是否已经有Boss血条
            if (mutationKing.hasMetadata("mutationKingBossBar")) {
                bossBar = (BossBar) mutationKing.getMetadata("mutationKingBossBar").get(0).value();
                if (debugMode) {
                    logger.info("IDC22异变之王使用现有Boss血条");
                }
            } else {
                // 创建新的Boss血条
                bossBar = Bukkit.createBossBar("§4异变之王", BarColor.RED, BarStyle.SEGMENTED_20);
                bossBar.setProgress(1.0);

                // 为所有在线玩家显示Boss血条
                for (Player player : Bukkit.getOnlinePlayers()) {
                    bossBar.addPlayer(player);
                }

                // 存储Boss血条引用
                mutationKing.setMetadata("mutationKingBossBar", new FixedMetadataValue(plugin, bossBar));

                if (debugMode) {
                    logger.info("IDC22异变之王创建新Boss血条");
                }
            }
        }

        // 冷却时间记录
        final Map<String, Long> lastAttackTimes = new HashMap<>();
        lastAttackTimes.put("crystal", 0L);
        lastAttackTimes.put("breath", 0L);
        lastAttackTimes.put("enderField", 0L);
        lastAttackTimes.put("summon", 0L);
        lastAttackTimes.put("obsidian", 0L);
        lastAttackTimes.put("fence", 0L);
        lastAttackTimes.put("obsidianBlocks", 0L); // 添加黑曜石块攻击冷却记录

        // 任务1：Boss血条更新
        if (bossBarEnabled && bossBar != null) {
            final BossBar finalBossBar = bossBar;
            BukkitTask bossBarTask = new BukkitRunnable() {
                @Override
                public void run() {
                    if (mutationKing == null || mutationKing.isDead() || !mutationKing.isValid()) {
                        finalBossBar.removeAll();
                        this.cancel();
                        return;
                    }

                    // 更新Boss血条
                    finalBossBar.setTitle("§4异变之王 §7- §c" + (int) mutationKing.getHealth() + "/" + (int) mutationKing.getMaxHealth() + " HP");
                    double progress = mutationKing.getHealth() / mutationKing.getMaxHealth();
                    finalBossBar.setProgress(Math.max(0, Math.min(1, progress)));

                    // 确保所有玩家都能看到血条
                    for (Player player : Bukkit.getOnlinePlayers()) {
                        if (!finalBossBar.getPlayers().contains(player)) {
                            finalBossBar.addPlayer(player);
                        }
                    }
                }
            }.runTaskTimer(plugin, 0L, 20L); // 立即开始，每秒更新一次

            tasks.add(bossBarTask);
        }

        // 任务2：智能移动AI + 技能触发
        if (movementAiEnabled) {
            BukkitTask movementTask = new BukkitRunnable() {
                private int tickCounter = 0;

                @Override
                public void run() {
                    tickCounter++;

                    // 检查实体是否还存在
                    if (mutationKing == null || mutationKing.isDead() || !mutationKing.isValid()) {
                        this.cancel();
                        // 清理任务记录
                        particleEffectTasks.remove(mutationKing);
                        // 清理Boss血条
                        if (mutationKing.hasMetadata("mutationKingBossBar")) {
                            BossBar bar = (BossBar) mutationKing.getMetadata("mutationKingBossBar").get(0).value();
                            bar.removeAll();
                        }
                        return;
                    }

                    // 执行移动AI
                    Player target = performMutationKingMovementAI(mutationKing);

                    // 如果有目标，尝试触发技能
                    if (target != null) {
                        long currentTime = System.currentTimeMillis();

                        // 每20tick（1秒）检查一次技能触发
                        if (tickCounter % 20 == 0) {
                            // 随机选择技能（扩展到7个技能）
                            int skillChoice = (int) (Math.random() * 7);

                            switch (skillChoice) {
                                case 0:
                                    // 末影水晶攻击
                                    if (crystalAttackEnabled && currentTime - lastAttackTimes.get("crystal") > crystalCooldown) {
                                        performMutationKingCrystalAttack(mutationKing, target);
                                        lastAttackTimes.put("crystal", currentTime);
                                        if (debugMode) {
                                            logger.info("IDC22异变之王触发末影水晶攻击，冷却时间: " + crystalCooldown + "ms");
                                        }
                                    }
                                    break;
                                case 1:
                                    // 龙息攻击
                                    if (breathAttackEnabled && currentTime - lastAttackTimes.get("breath") > breathCooldown) {
                                        performMutationKingBreathAttack(mutationKing, target);
                                        lastAttackTimes.put("breath", currentTime);
                                    }
                                    break;
                                case 2:
                                    // 黑曜石块连续攻击（独立冷却）
                                    if (obsidianAttackEnabled && currentTime - lastAttackTimes.get("obsidianBlocks") > obsidianBlocksCooldown) {
                                        performMutationKingShootObsidianBlocks(mutationKing, target);
                                        lastAttackTimes.put("obsidianBlocks", currentTime);
                                        if (debugMode) {
                                            logger.info("IDC22异变之王触发黑曜石块攻击，冷却时间: " + obsidianBlocksCooldown + "ms");
                                        }
                                    }
                                    break;
                                case 3:
                                    // 黑曜石柱攻击（独立冷却）
                                    if (obsidianAttackEnabled && currentTime - lastAttackTimes.get("obsidian") > obsidianCooldown) {
                                        performMutationKingObsidianPillarAttack(mutationKing, target);
                                        lastAttackTimes.put("obsidian", currentTime);
                                        if (debugMode) {
                                            logger.info("IDC22异变之王触发黑曜石柱攻击，冷却时间: " + obsidianCooldown + "ms");
                                        }
                                    }
                                    break;
                                case 4:
                                    // 下界栅栏攻击（独立冷却）
                                    if (fenceAttackEnabled && currentTime - lastAttackTimes.get("fence") > fenceCooldown) {
                                        performMutationKingNetherFenceAttack(mutationKing, target);
                                        lastAttackTimes.put("fence", currentTime);
                                    }
                                    break;
                                case 5:
                                    // 末影粒子减速场
                                    if (enderFieldEnabled && currentTime - lastAttackTimes.get("enderField") > enderFieldCooldown) {
                                        performMutationKingEnderField(mutationKing, target);
                                        lastAttackTimes.put("enderField", currentTime);
                                    }
                                    break;
                                case 6:
                                    // 召唤变异生物
                                    if (summonEnabled && currentTime - lastAttackTimes.get("summon") > summonCooldown) {
                                        performMutationKingSummon(mutationKing, target);
                                        lastAttackTimes.put("summon", currentTime);
                                    }
                                    break;
                            }
                        }
                    }
                }
            }.runTaskTimer(plugin, 5, movementInterval); // 0.25秒后开始，按配置间隔执行

            tasks.add(movementTask);
        }

        // 记录任务以便清理
        if (!tasks.isEmpty()) {
            // 使用第一个任务作为代表存储（简化处理）
            particleEffectTasks.put(mutationKing, tasks.get(0));
        }

        if (debugMode) {
            logger.info("IDC22异变之王技能任务已启动 - 移动AI: " + movementAiEnabled +
                       ", Boss血条: " + bossBarEnabled + ", 所有技能已启用");
        }
    }

    /**
     * 执行异变之王智能移动AI（简化版，基于原版MutationKing逻辑）
     * @return 当前目标玩家，如果没有目标则返回null
     */
    private Player performMutationKingMovementAI(org.bukkit.entity.EnderDragon mutationKing) {
        // 寻找最近的玩家
        Player target = null;
        double minDistance = Double.MAX_VALUE;

        for (Entity entity : mutationKing.getNearbyEntities(50, 50, 50)) {
            if (entity instanceof Player) {
                Player player = (Player) entity;
                if (player.getGameMode() != GameMode.SPECTATOR && player.getGameMode() != GameMode.CREATIVE) {
                    double distance = mutationKing.getLocation().distance(player.getLocation());
                    if (distance < minDistance) {
                        minDistance = distance;
                        target = player;
                    }
                }
            }
        }

        if (target != null) {
            // 计算方向向量
            Vector direction = target.getLocation().subtract(mutationKing.getLocation()).toVector().normalize();

            // 根据距离调整行为
            if (minDistance > 15.0) {
                // 距离较远，快速接近
                Vector moveVector = direction.clone().multiply(1.5);
                mutationKing.setVelocity(moveVector);

                // 播放飞行音效
                if (Math.random() < 0.1) { // 10%几率播放音效
                    mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_ENDER_DRAGON_FLAP, 0.8f, 1.2f);
                }
            } else if (minDistance > 5.0) {
                // 中等距离，慢速接近，准备攻击
                Vector moveVector = direction.clone().multiply(0.8);
                mutationKing.setVelocity(moveVector);

                // 播放警告音效
                if (Math.random() < 0.05) { // 5%几率播放警告音效
                    mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_ENDER_DRAGON_GROWL, 0.5f, 1.0f);
                }
            } else {
                // 距离较近，执行攻击行为
                if (Math.random() < 0.4) { // 40%几率冲锋
                    // 冲锋攻击
                    Vector chargeVector = direction.clone().multiply(2.5);
                    chargeVector.setY(chargeVector.getY() * 0.5); // 减少Y轴分量，使冲锋更加水平
                    mutationKing.setVelocity(chargeVector);

                    // 播放冲锋音效
                    mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.0f, 0.8f);

                    // 冲锋粒子效果
                    mutationKing.getWorld().spawnParticle(Particle.DRAGON_BREATH, mutationKing.getLocation(), 20, 1.0, 1.0, 1.0, 0.1);
                }

                // 近距离攻击检测
                if (minDistance < 3.0) {
                    // 造成近战伤害
                    target.damage(20.0, mutationKing);

                    // 击退效果
                    Vector knockback = target.getLocation().toVector().subtract(mutationKing.getLocation().toVector()).normalize().multiply(1.5).setY(0.5);
                    target.setVelocity(knockback);

                    // 播放攻击音效
                    target.getWorld().playSound(target.getLocation(), Sound.ENTITY_ENDER_DRAGON_HURT, 0.5f, 1.0f);

                    if (debugMode) {
                        logger.info("IDC22异变之王近战攻击命中玩家: " + target.getName() + "，伤害: 20.0");
                    }
                }
            }
        }

        return target; // 返回当前目标玩家
    }

    /**
     * 执行异变之王末影水晶攻击（基于原版MutationKing实现）
     */
    private void performMutationKingCrystalAttack(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 获取龙的位置
        Location dragonLoc = mutationKing.getLocation();

        // 播放主要攻击音效
        mutationKing.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDER_DRAGON_SHOOT, 2.0f, 0.5f);
        mutationKing.getWorld().playSound(dragonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.5f, 0.7f);

        // 显示准备攻击的粒子效果
        mutationKing.getWorld().spawnParticle(Particle.END_ROD, dragonLoc, 80, 2.0, 2.0, 2.0, 0.1);
        mutationKing.getWorld().spawnParticle(Particle.PORTAL, dragonLoc, 50, 1.5, 1.5, 1.5, 0.05);

        // 创建环绕龙的末影水晶
        int orbitalCrystals = 3; // 环绕的水晶数量
        List<org.bukkit.entity.EnderCrystal> orbitalCrystalList = new ArrayList<>();

        // 创建环绕水晶
        for (int i = 0; i < orbitalCrystals; i++) {
            double angle = (Math.PI * 2 * i) / orbitalCrystals;
            double x = Math.cos(angle) * 3;
            double z = Math.sin(angle) * 3;

            Location orbitalLoc = dragonLoc.clone().add(x, 1, z);

            // 生成环绕水晶
            org.bukkit.entity.EnderCrystal orbitalCrystal = (org.bukkit.entity.EnderCrystal) mutationKing.getWorld().spawnEntity(orbitalLoc, EntityType.END_CRYSTAL);
            orbitalCrystal.setShowingBottom(false);
            orbitalCrystal.setGlowing(true);
            orbitalCrystal.setInvulnerable(true);
            orbitalCrystal.setMetadata("mutationKingOrbitalCrystal", new FixedMetadataValue(plugin, true));

            // 添加到列表
            orbitalCrystalList.add(orbitalCrystal);

            // 显示生成效果
            mutationKing.getWorld().spawnParticle(Particle.END_ROD, orbitalLoc, 30, 1.0, 1.0, 1.0, 0.1);
            orbitalLoc.getWorld().playSound(orbitalLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 0.8f, 1.2f);
        }

        // 创建环绕任务
        new BukkitRunnable() {
            private int ticks = 0;
            private final int MAX_TICKS = 200; // 10秒生命周期
            private final int SHOOT_INTERVAL = 20; // 每1秒发射一次
            private double orbitalAngle = 0;

            @Override
            public void run() {
                // 检查龙是否还存在
                if (mutationKing == null || !mutationKing.isValid() || mutationKing.isDead() || ticks >= MAX_TICKS) {
                    // 移除所有环绕水晶
                    for (org.bukkit.entity.EnderCrystal crystal : orbitalCrystalList) {
                        if (crystal != null && !crystal.isDead()) {
                            // 爆炸效果
                            Location explodeLoc = crystal.getLocation();
                            explodeLoc.getWorld().spawnParticle(Particle.EXPLOSION, explodeLoc, 10, 0.5, 0.5, 0.5, 0.1);
                            explodeLoc.getWorld().playSound(explodeLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);
                            crystal.remove();
                        }
                    }
                    this.cancel();
                    return;
                }

                // 更新龙的当前位置
                Location currentDragonLoc = mutationKing.getLocation();

                // 更新环绕角度
                orbitalAngle += Math.PI / 16; // 每次旋转11.25度

                // 更新环绕水晶位置
                for (int i = 0; i < orbitalCrystalList.size(); i++) {
                    org.bukkit.entity.EnderCrystal crystal = orbitalCrystalList.get(i);

                    if (crystal != null && !crystal.isDead()) {
                        // 计算新位置
                        double angle = orbitalAngle + ((Math.PI * 2 * i) / orbitalCrystals);
                        double x = Math.cos(angle) * 3;
                        double y = Math.sin(angle * 0.5) * 0.5 + 1; // 添加上下波动
                        double z = Math.sin(angle) * 3;

                        // 设置新位置
                        Location newLoc = currentDragonLoc.clone().add(x, y, z);
                        crystal.teleport(newLoc);

                        // 添加粒子效果
                        if (ticks % 5 == 0) {
                            crystal.getWorld().spawnParticle(Particle.END_ROD, crystal.getLocation(), 5, 0.2, 0.2, 0.2, 0.02);
                        }

                        // 治疗龙
                        if (ticks % 40 == 0) { // 每2秒治疗一次
                            double currentHealth = mutationKing.getHealth();
                            double maxHealth = mutationKing.getMaxHealth();
                            if (currentHealth < maxHealth) {
                                mutationKing.setHealth(Math.min(currentHealth + 50.0, maxHealth)); // 恢复50点生命值

                                // 显示治疗效果
                                mutationKing.getWorld().spawnParticle(Particle.HEART, mutationKing.getLocation(), 20, 1.5, 1.5, 1.5, 0.1);
                                mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 0.5f, 0.8f);
                            }
                        }
                    }
                }

                // 定期发射水晶子弹
                if (ticks % SHOOT_INTERVAL == 0 && target != null && target.isOnline()) {
                    // 从每个环绕水晶发射一个子弹
                    for (org.bukkit.entity.EnderCrystal crystal : orbitalCrystalList) {
                        if (crystal != null && !crystal.isDead()) {
                            shootMutationKingCrystalBullet(mutationKing, crystal.getLocation(), target);
                        }
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 5, 1); // 0.25秒后开始，每0.05秒执行一次

        if (debugMode) {
            logger.info("IDC22异变之王发动末影水晶攻击，目标: " + target.getName());
        }
    }

    /**
     * 从指定位置发射末影水晶子弹（基于原版MutationKing实现）
     */
    private void shootMutationKingCrystalBullet(org.bukkit.entity.EnderDragon mutationKing, Location startLoc, Player target) {
        // 计算方向向量
        Vector direction = target.getLocation().add(0, 1, 0).subtract(startLoc).toVector().normalize();

        // 播放发射音效
        startLoc.getWorld().playSound(startLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 1.5f);

        // 显示发射粒子效果
        startLoc.getWorld().spawnParticle(Particle.END_ROD, startLoc, 15, 0.2, 0.2, 0.2, 0.1);

        // 创建子弹实体（使用盔甲架作为视觉效果）
        org.bukkit.entity.ArmorStand bullet = (org.bukkit.entity.ArmorStand) startLoc.getWorld().spawnEntity(startLoc, EntityType.ARMOR_STAND);
        bullet.setSmall(true);
        bullet.setInvisible(true);
        bullet.setInvulnerable(true);
        bullet.setGravity(false);
        bullet.setMarker(true);
        bullet.setMetadata("mutationKingCrystalBullet", new FixedMetadataValue(plugin, true));

        // 创建子弹追踪任务
        new BukkitRunnable() {
            private int ticks = 0;
            private final int MAX_TICKS = 60; // 3秒生命周期
            private Vector velocity = direction.clone().multiply(1.2); // 增加速度

            @Override
            public void run() {
                if (bullet == null || !bullet.isValid() || ticks >= MAX_TICKS) {
                    if (bullet != null && bullet.isValid()) {
                        // 爆炸效果
                        Location explodeLoc = bullet.getLocation();
                        explodeLoc.getWorld().createExplosion(explodeLoc, 0.0f, false, false);
                        explodeLoc.getWorld().spawnParticle(Particle.EXPLOSION, explodeLoc, 10, 0.5, 0.5, 0.5, 0.1);
                        explodeLoc.getWorld().playSound(explodeLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);

                        // 移除子弹
                        bullet.remove();
                    }
                    this.cancel();
                    return;
                }

                // 更新子弹位置
                Location currentLoc = bullet.getLocation();
                Location newLoc = currentLoc.clone().add(velocity);
                bullet.teleport(newLoc);

                // 显示粒子效果
                bullet.getWorld().spawnParticle(Particle.END_ROD, newLoc, 3, 0.1, 0.1, 0.1, 0.02);

                // 检测碰撞
                for (Entity entity : newLoc.getWorld().getNearbyEntities(newLoc, 1.0, 1.0, 1.0)) {
                    if (entity instanceof Player && entity != mutationKing) {
                        Player hitPlayer = (Player) entity;

                        // 造成伤害
                        hitPlayer.damage(15.0, mutationKing);

                        // 播放命中音效
                        hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                        // 显示命中粒子效果
                        hitPlayer.getWorld().spawnParticle(Particle.EXPLOSION, hitPlayer.getLocation(), 10, 0.5, 0.5, 0.5, 0.1);

                        if (debugMode) {
                            logger.info("IDC22末影水晶子弹命中玩家: " + hitPlayer.getName() + "，伤害: 15.0");
                        }

                        // 移除子弹
                        bullet.remove();
                        this.cancel();
                        return;
                    }
                }

                ticks++;
            }
        }.runTaskTimer(plugin, 0, 1); // 立即开始，每tick执行一次
    }

    /**
     * 执行异变之王龙息攻击（基于原版MutationKing实现）
     */
    private void performMutationKingBreathAttack(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 简化实现：创建龙息效果云
        Location targetLoc = target.getLocation();

        // 播放龙息音效
        mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_ENDER_DRAGON_AMBIENT, 2.0f, 0.8f);

        // 创建多个龙息效果云
        for (int i = 0; i < 5; i++) {
            Location breathLoc = targetLoc.clone().add(
                (Math.random() - 0.5) * 6,
                Math.random() * 2,
                (Math.random() - 0.5) * 6
            );

            org.bukkit.entity.AreaEffectCloud breathCloud = (org.bukkit.entity.AreaEffectCloud)
                mutationKing.getWorld().spawnEntity(breathLoc, EntityType.AREA_EFFECT_CLOUD);
            breathCloud.setRadius(3.0f);
            breathCloud.setDuration(100); // 5秒
            breathCloud.addCustomEffect(new PotionEffect(PotionEffectType.INSTANT_DAMAGE, 1, 1), true);
            breathCloud.setParticle(Particle.DRAGON_BREATH);
            breathCloud.setMetadata("mutationKingBreath", new FixedMetadataValue(plugin, true));
        }

        if (debugMode) {
            logger.info("IDC22异变之王发动龙息攻击，目标: " + target.getName());
        }
    }

    /**
     * 执行异变之王黑曜石柱攻击（基于原版MutationKing.createObsidianPillarAttack实现）
     */
    private void performMutationKingObsidianPillarAttack(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 获取IDC22配置参数
        EntityOverrideConfig config = getIDC22Config();
        int pillarCount = 3; // 默认3个柱子
        int randomRange = 8; // 默认8格范围

        if (config != null && config.skillCooldownOverrides != null) {
            pillarCount = config.skillCooldownOverrides.getOrDefault("obsidian_pillar_count", 3);
            randomRange = config.skillCooldownOverrides.getOrDefault("obsidian_pillar_random_range", 8);
        }

        // 获取玩家位置
        Location playerLoc = target.getLocation().clone();

        // 播放攻击音效
        mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.5f);
        mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.7f);

        // 创建多个2*2的黑曜石柱，无限向上延伸直到收回
        List<Location> pillarBaseLocations = new ArrayList<>();

        // 主柱 - 玩家位置
        pillarBaseLocations.add(playerLoc.clone());

        // 随机位置的额外柱子
        for (int i = 0; i < pillarCount - 1; i++) {
            Location randomLoc = playerLoc.clone().add(
                (Math.random() - 0.5) * randomRange, 0, (Math.random() - 0.5) * randomRange
            );
            pillarBaseLocations.add(randomLoc);
        }

        // 为每个柱子创建任务
        for (Location baseLoc : pillarBaseLocations) {
            createSingleObsidianPillar(mutationKing, baseLoc, config);
        }

        if (debugMode) {
            logger.info("IDC22异变之王发动黑曜石柱攻击，目标: " + target.getName() + "，柱子数量: " + pillarCount + "，范围: " + randomRange);
        }
    }

    /**
     * 创建单个黑曜石柱（基于原版MutationKing实现）
     */
    private void createSingleObsidianPillar(org.bukkit.entity.EnderDragon mutationKing, Location baseLoc, EntityOverrideConfig config) {
        // 从配置中获取参数
        int maxGrowthTicks = 160; // 默认8秒
        int growthInterval = 2; // 默认0.1秒
        double pillarDamage = 18.0; // 默认18点伤害

        if (config != null && config.skillCooldownOverrides != null) {
            maxGrowthTicks = config.skillCooldownOverrides.getOrDefault("obsidian_pillar_growth_time", 160);
            growthInterval = config.skillCooldownOverrides.getOrDefault("obsidian_pillar_growth_interval", 2);
            pillarDamage = config.skillCooldownOverrides.getOrDefault("obsidian_pillar_damage", 18).doubleValue();
        }

        final int finalMaxGrowthTicks = maxGrowthTicks;
        final int finalGrowthInterval = growthInterval;
        final double finalPillarDamage = pillarDamage;

        // 创建2*2的黑曜石柱，无限向上延伸直到收回
        new BukkitRunnable() {
            private int height = 0;
            private int growthTicks = 0;
            private boolean isGrowing = true; // 是否处于生长阶段
            private final List<Location> pillarBlocks = new ArrayList<>();

            @Override
            public void run() {
                // 检查龙是否还存在
                if (mutationKing == null || !mutationKing.isValid() || mutationKing.isDead()) {
                    // 清理所有方块
                    for (Location loc : pillarBlocks) {
                        if (loc.getBlock().getType() == Material.OBSIDIAN) {
                            loc.getBlock().setType(Material.AIR);
                        }
                    }
                    this.cancel();
                    return;
                }

                growthTicks++;

                // 检查是否应该切换到收回阶段
                if (isGrowing && growthTicks >= finalMaxGrowthTicks) {
                    isGrowing = false;
                }

                // 播放音效（每20tick播放一次）
                if (growthTicks % 20 == 0) {
                    baseLoc.getWorld().playSound(baseLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.5f);
                }

                if (isGrowing) {
                    // 生长阶段 - 向上延伸
                    growObsidianPillar();
                } else {
                    // 收回阶段 - 从顶部开始消除
                    if (pillarBlocks.isEmpty()) {
                        // 所有方块都已清理，取消任务
                        this.cancel();
                        return;
                    }
                    shrinkObsidianPillar();
                }
            }

            // 生长黑曜石柱
            private void growObsidianPillar() {
                // 在2*2区域创建黑曜石方块
                for (int x = 0; x < 2; x++) {
                    for (int z = 0; z < 2; z++) {
                        Location blockLoc = baseLoc.clone().add(x, height, z);

                        // 检查位置是否可以放置方块
                        if (blockLoc.getBlock().getType() == Material.AIR
                                || blockLoc.getBlock().getType() == Material.CAVE_AIR
                                || blockLoc.getBlock().getType() == Material.WATER) {

                            // 设置为黑曜石
                            blockLoc.getBlock().setType(Material.OBSIDIAN);
                            pillarBlocks.add(blockLoc.clone());

                            // 检测是否击中玩家
                            for (Entity entity : blockLoc.getWorld().getNearbyEntities(blockLoc, 1.0, 1.0, 1.0)) {
                                if (entity instanceof Player) {
                                    Player hitPlayer = (Player) entity;
                                    // 造成伤害
                                    hitPlayer.damage(finalPillarDamage, mutationKing);
                                    // 播放受伤音效
                                    hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                                    if (debugMode) {
                                        logger.info("IDC22黑曜石柱命中玩家: " + hitPlayer.getName() + "，伤害: " + finalPillarDamage);
                                    }
                                }
                            }
                        }
                    }
                }

                // 显示粒子效果
                baseLoc.getWorld().spawnParticle(Particle.LAVA,
                        baseLoc.clone().add(1.0, height, 1.0), 20,
                        1.0, 0.1, 1.0, 0);

                height++;
            }

            // 收缩黑曜石柱
            private void shrinkObsidianPillar() {
                // 获取最高的4个方块（2x2区域的顶层）
                List<Location> topBlocks = new ArrayList<>();
                int maxY = -1;

                // 找出最高的Y坐标
                for (Location loc : pillarBlocks) {
                    if (loc.getBlockY() > maxY) {
                        maxY = loc.getBlockY();
                    }
                }

                // 收集最高层的方块
                for (Location loc : new ArrayList<>(pillarBlocks)) {
                    if (loc.getBlockY() == maxY) {
                        topBlocks.add(loc);
                    }
                }

                // 移除最高层的方块
                for (Location loc : topBlocks) {
                    if (loc.getBlock().getType() == Material.OBSIDIAN) {
                        loc.getBlock().setType(Material.AIR);

                        // 显示破碎粒子效果
                        loc.getWorld().spawnParticle(Particle.LAVA,
                                loc.clone().add(0.5, 0.5, 0.5), 10,
                                0.3, 0.3, 0.3, 0);
                    }
                    pillarBlocks.remove(loc);
                }
            }
        }.runTaskTimer(plugin, 0L, finalGrowthInterval); // 立即开始，按配置间隔执行
    }

    /**
     * 执行异变之王黑曜石快连续攻击（基于原版MutationKing.shootObsidianBlocks实现）
     */
    private void performMutationKingShootObsidianBlocks(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 获取IDC22配置参数
        EntityOverrideConfig config = getIDC22Config();
        int waves = 5; // 默认5波
        int maxBlocksPerWave = 7; // 默认最多7个方块
        int waveInterval = 40; // 默认2秒间隔
        int cleanupDelay = 40; // 默认2秒清理延迟
        double blockDamage = 12.0; // 默认12点伤害
        double blockSpeed = 1.5; // 默认1.5倍速度
        int spreadRange = 6; // 默认6格散布范围

        // 新增配置项
        boolean instantCleanup = true; // 默认立即清理（原版行为）

        if (config != null && config.skillCooldownOverrides != null) {
            waves = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_waves", 5);
            maxBlocksPerWave = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_per_wave", 7);
            waveInterval = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_wave_interval", 40);
            cleanupDelay = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_cleanup_delay", 0); // 默认0=立即清理
            blockDamage = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_damage", 12).doubleValue();

            // 检查是否启用立即清理（原版行为）
            if (config.skillCooldownOverrides.containsKey("obsidian_blocks_instant_cleanup")) {
                instantCleanup = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_instant_cleanup", 1) != 0;
            }

            // 从配置中加载速度和散布范围
            if (config.skillCooldownOverrides.containsKey("obsidian_blocks_speed")) {
                blockSpeed = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_speed", 15).doubleValue() / 10.0; // 15 -> 1.5
            }
            if (config.skillCooldownOverrides.containsKey("obsidian_blocks_spread")) {
                spreadRange = config.skillCooldownOverrides.getOrDefault("obsidian_blocks_spread", 6);
            }

            if (debugMode) {
                String cleanupMode = instantCleanup ? "立即清理（原版模式）" : "延迟清理（" + (cleanupDelay/20.0) + "秒）";
                logger.info("IDC22黑曜石块攻击配置加载 - 波数: " + waves + ", 每波数量: " + maxBlocksPerWave +
                           ", 波次间隔: " + waveInterval + "tick, 清理模式: " + cleanupMode);
            }
        }

        final int finalWaves = waves;
        final int finalMaxBlocksPerWave = maxBlocksPerWave;
        final int finalWaveInterval = waveInterval;
        final int finalCleanupDelay = cleanupDelay;
        final boolean finalInstantCleanup = instantCleanup;
        final double finalBlockDamage = blockDamage;
        final double finalBlockSpeed = blockSpeed;
        final int finalSpreadRange = spreadRange;

        // 获取龙的位置和方向
        Location dragonLoc = mutationKing.getLocation();

        // 播放发射音效
        mutationKing.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.5f);
        mutationKing.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.7f);

        // 存储所有生成的黑曜石方块
        Set<org.bukkit.entity.FallingBlock> activeBlocks = new HashSet<>();

        // 持续发射黑曜石方块
        BukkitTask mainTask = new BukkitRunnable() {
            private int wavesRemaining = finalWaves; // 按配置发射波数

            @Override
            public void run() {
                if (wavesRemaining <= 0 || mutationKing == null || !mutationKing.isValid() || mutationKing.isDead()) {
                    // 清理所有剩余的方块
                    cleanupRemainingBlocks();
                    this.cancel();
                    return;
                }

                // 每波发射多个黑曜石方块
                int blockCount = Math.max(1, finalMaxBlocksPerWave - 2) + (int)(Math.random() * 4); // 按配置数量随机

                // 重新计算方向，以便追踪移动的玩家
                Vector currentDirection = target.getLocation().subtract(mutationKing.getLocation()).toVector().normalize();

                // 播放波次音效
                mutationKing.getWorld().playSound(mutationKing.getLocation(), Sound.ENTITY_WITHER_SHOOT, 0.8f, 1.2f);

                // 发射这一波的黑曜石方块
                for (int i = 0; i < blockCount; i++) {
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            // 检查龙是否还存在
                            if (mutationKing == null || !mutationKing.isValid() || mutationKing.isDead()) {
                                return;
                            }

                            // 计算发射位置
                            Location currentDragonLoc = mutationKing.getLocation();
                            Location spawnLoc = currentDragonLoc.clone().add(currentDirection.clone().multiply(5));
                            spawnLoc.add(Math.random() * finalSpreadRange - finalSpreadRange/2.0, Math.random() * 3, Math.random() * finalSpreadRange - finalSpreadRange/2.0); // 按配置散布范围

                            try {
                                // 创建黑曜石方块
                                org.bukkit.entity.FallingBlock block = spawnLoc.getWorld().spawnFallingBlock(spawnLoc, Material.OBSIDIAN.createBlockData());

                                // 添加到活动方块集合
                                activeBlocks.add(block);

                                // 设置方块速度
                                Vector velocity = currentDirection.clone().multiply(finalBlockSpeed + Math.random() * 0.5);
                                velocity.add(new Vector((Math.random() - 0.5) * 0.3, Math.random() * 0.2, (Math.random() - 0.5) * 0.3));
                                block.setVelocity(velocity);

                                // 设置方块属性
                                block.setDropItem(false);
                                block.setMetadata("mutationKingBlock", new FixedMetadataValue(plugin, true));

                                // 播放发射音效
                                spawnLoc.getWorld().playSound(spawnLoc, Sound.ENTITY_WITHER_SHOOT, 0.3f, 1.5f);

                                // 显示发射粒子效果
                                spawnLoc.getWorld().spawnParticle(Particle.LAVA, spawnLoc, 5, 0.2, 0.2, 0.2, 0.05);

                                // 创建方块追踪任务
                                new BukkitRunnable() {
                                    private int ticks = 0;
                                    private final int maxTicks = 100; // 5秒生命周期

                                    @Override
                                    public void run() {
                                        // 检查方块是否还存在
                                        if (block == null || block.isDead() || ticks >= maxTicks) {
                                            // 移除方块
                                            if (block != null && !block.isDead()) {
                                                // 爆炸效果
                                                Location explodeLoc = block.getLocation();
                                                explodeLoc.getWorld().spawnParticle(Particle.EXPLOSION, explodeLoc, 10, 0.5, 0.5, 0.5, 0.1);
                                                explodeLoc.getWorld().playSound(explodeLoc, Sound.ENTITY_GENERIC_EXPLODE, 0.8f, 1.0f);
                                                block.remove();
                                            }
                                            activeBlocks.remove(block);
                                            this.cancel();
                                            return;
                                        }

                                        // 检测碰撞
                                        for (Entity entity : block.getWorld().getNearbyEntities(block.getLocation(), 2.0, 2.0, 2.0)) {
                                            if (entity instanceof Player && entity != mutationKing) {
                                                Player hitPlayer = (Player) entity;

                                                // 造成伤害
                                                hitPlayer.damage(finalBlockDamage, mutationKing);

                                                // 播放命中音效
                                                hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                                                // 显示命中粒子效果
                                                hitPlayer.getWorld().spawnParticle(Particle.EXPLOSION, hitPlayer.getLocation(), 10, 0.5, 0.5, 0.5, 0.1);

                                                if (debugMode) {
                                                    logger.info("IDC22黑曜石快攻击命中玩家: " + hitPlayer.getName() + "，伤害: " + finalBlockDamage);
                                                }

                                                // 移除方块
                                                block.remove();
                                                activeBlocks.remove(block);
                                                this.cancel();
                                                return;
                                            }
                                        }

                                        // 检查方块是否已经落地变成了实体方块
                                        if (block.isDead() && !activeBlocks.contains(block)) {
                                            // 检查方块落地的位置
                                            Location landedLoc = block.getLocation().clone();

                                            // 向下搜索几个方块，找到可能的黑曜石
                                            for (int y = 0; y >= -5; y--) {
                                                Location checkLoc = landedLoc.clone().add(0, y, 0);
                                                if (checkLoc.getBlock().getType() == Material.OBSIDIAN) {
                                                    if (finalInstantCleanup || finalCleanupDelay <= 0) {
                                                        // 立即清理（原版行为）
                                                        checkLoc.getBlock().setType(Material.AIR);
                                                        checkLoc.getWorld().spawnParticle(Particle.LAVA, checkLoc, 5, 0.2, 0.2, 0.2, 0.05);

                                                        if (debugMode) {
                                                            logger.info("IDC22黑曜石块立即清理（原版模式），位置: " + checkLoc.getBlockX() + "," + checkLoc.getBlockY() + "," + checkLoc.getBlockZ());
                                                        }
                                                    } else {
                                                        // 延迟清理（增强模式）
                                                        if (debugMode) {
                                                            logger.info("IDC22黑曜石块落地，将在 " + finalCleanupDelay + "tick (" + (finalCleanupDelay/20.0) + "秒) 后清理");
                                                        }

                                                        new BukkitRunnable() {
                                                            @Override
                                                            public void run() {
                                                                if (checkLoc.getBlock().getType() == Material.OBSIDIAN) {
                                                                    checkLoc.getBlock().setType(Material.AIR);
                                                                    checkLoc.getWorld().spawnParticle(Particle.LAVA, checkLoc, 5, 0.2, 0.2, 0.2, 0.05);

                                                                    if (debugMode) {
                                                                        logger.info("IDC22黑曜石块延迟清理完成，位置: " + checkLoc.getBlockX() + "," + checkLoc.getBlockY() + "," + checkLoc.getBlockZ());
                                                                    }
                                                                }
                                                            }
                                                        }.runTaskLater(plugin, finalCleanupDelay);
                                                    }
                                                    break;
                                                }
                                            }
                                            this.cancel();
                                            return;
                                        }

                                        ticks++;
                                    }
                                }.runTaskTimer(plugin, 5, 2); // 0.25秒后开始，每0.1秒执行一次
                            } catch (Exception e) {
                                if (debugMode) {
                                    logger.warning("发射黑曜石方块时出错: " + e.getMessage());
                                }
                            }
                        }
                    }.runTaskLater(plugin, i * 3); // 每0.15秒发射一个方块
                }

                wavesRemaining--;
            }

            // 清理剩余方块
            private void cleanupRemainingBlocks() {
                // 清理所有剩余的飞行中的黑曜石方块
                for (org.bukkit.entity.FallingBlock block : new HashSet<>(activeBlocks)) {
                    if (block != null && !block.isDead()) {
                        // 爆炸效果
                        block.getWorld().spawnParticle(Particle.EXPLOSION, block.getLocation(), 5, 0.2, 0.2, 0.2, 0.05);
                        block.remove();
                    }
                }

                // 清空集合
                activeBlocks.clear();
            }
        }.runTaskTimer(plugin, 0L, finalWaveInterval); // 立即开始，按配置间隔发射一波

        if (debugMode) {
            logger.info("IDC22异变之王发动黑曜石快连续攻击，目标: " + target.getName());
        }
    }

    /**
     * 执行异变之王下界栅栏攻击（基于原版MutationKing.shootNetherFenceAttack实现）
     */
    private void performMutationKingNetherFenceAttack(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 获取IDC22配置参数
        EntityOverrideConfig config = getIDC22Config();
        int rayCount = 5; // 默认5条射线
        int startDelay = 5; // 默认0.25秒延迟

        if (config != null && config.skillCooldownOverrides != null) {
            rayCount = config.skillCooldownOverrides.getOrDefault("fence_ray_count", 5);
            startDelay = config.skillCooldownOverrides.getOrDefault("fence_ray_start_delay", 5);
        }

        // 获取龙的位置和方向
        Location dragonLoc = mutationKing.getLocation();
        Vector mainDirection = target.getLocation().subtract(dragonLoc).toVector().normalize();

        // 播放攻击音效
        dragonLoc.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_SHOOT, 1.0f, 0.5f);
        dragonLoc.getWorld().playSound(dragonLoc, Sound.ENTITY_WITHER_AMBIENT, 1.0f, 0.7f);

        // 计算多个方向向量（可配置角度）
        List<Vector> directions = new ArrayList<>();

        // 从配置中获取角度数组，默认为[0, 36, -36, 72, -72]
        List<Integer> angles = new ArrayList<>();
        angles.add(0);    // 主方向
        angles.add(36);   // 左侧36°
        angles.add(-36);  // 右侧36°
        angles.add(72);   // 左侧72°
        angles.add(-72);  // 右侧72°

        // 根据配置的射线数量调整
        for (int i = 0; i < Math.min(rayCount, angles.size()); i++) {
            if (angles.get(i) == 0) {
                directions.add(mainDirection.clone());
            } else {
                Vector rotatedDirection = rotateVectorY(mainDirection.clone(), Math.toRadians(angles.get(i)));
                directions.add(rotatedDirection);
            }
        }

        // 为每个方向创建下界栅栏射线
        for (int i = 0; i < directions.size(); i++) {
            final Vector direction = directions.get(i);
            final int rayIndex = i;

            // 延迟启动每条射线，创造波浪效果
            new BukkitRunnable() {
                @Override
                public void run() {
                    createMutationKingFenceRay(mutationKing, dragonLoc, direction, rayIndex, config);
                }
            }.runTaskLater(plugin, rayIndex * startDelay); // 按配置延迟启动
        }

        if (debugMode) {
            logger.info("IDC22异变之王发动下界栅栏攻击，目标: " + target.getName() + "，射线数量: " + directions.size());
        }
    }

    /**
     * 创建单条下界栅栏射线（基于原版MutationKing.createFenceRay实现）
     */
    private void createMutationKingFenceRay(org.bukkit.entity.EnderDragon mutationKing, Location startLoc, Vector direction, int rayIndex, EntityOverrideConfig config) {
        // 从配置中获取参数
        int maxDistance = 25; // 默认25格
        int cleanupDelay = 60; // 默认3秒
        double fenceDamage = 10.0; // 默认10点伤害
        int slownessLevel = 2; // 默认减速3
        int slownessDuration = 60; // 默认3秒

        if (config != null && config.skillCooldownOverrides != null) {
            maxDistance = config.skillCooldownOverrides.getOrDefault("fence_ray_max_distance", 25);
            cleanupDelay = config.skillCooldownOverrides.getOrDefault("fence_cleanup_delay", 60);
            fenceDamage = config.skillCooldownOverrides.getOrDefault("fence_damage", 10).doubleValue();
            slownessLevel = config.skillCooldownOverrides.getOrDefault("fence_slowness_level", 2);
            slownessDuration = config.skillCooldownOverrides.getOrDefault("fence_slowness_duration", 60);
        }

        final int finalMaxDistance = maxDistance;
        final int finalCleanupDelay = cleanupDelay;
        final double finalFenceDamage = fenceDamage;
        final int finalSlownessLevel = slownessLevel;
        final int finalSlownessDuration = slownessDuration;

        // 创建下界栅栏射线
        new BukkitRunnable() {
            private int distance = 0;
            private boolean hitObstacle = false;
            private final List<Location> fenceLocations = new ArrayList<>();
            private final Map<Location, Material> originalBlocks = new HashMap<>();

            @Override
            public void run() {
                // 检查龙是否还存在或达到最大距离
                if (mutationKing == null || !mutationKing.isValid() || mutationKing.isDead() || distance >= finalMaxDistance || hitObstacle) {
                    // 延迟移除下界栅栏
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            for (Location loc : new ArrayList<>(fenceLocations)) {
                                if (loc.getBlock().getType() == Material.NETHER_BRICK_FENCE) {
                                    // 恢复原始方块类型
                                    Material originalType = originalBlocks.getOrDefault(loc, Material.AIR);
                                    loc.getBlock().setType(originalType);

                                    // 显示破碎粒子效果
                                    loc.getWorld().spawnParticle(Particle.LAVA,
                                            loc.clone().add(0.5, 0.5, 0.5), 5,
                                            0.2, 0.2, 0.2, 0);
                                }
                            }
                        }
                    }.runTaskLater(plugin, finalCleanupDelay); // 按配置延迟移除下界栅栏

                    this.cancel();
                    return;
                }

                // 计算当前位置
                Location currentLoc = startLoc.clone().add(direction.clone().multiply(distance));

                // 检查当前位置是否是实体方块
                if (currentLoc.getBlock().getType().isSolid()
                        && currentLoc.getBlock().getType() != Material.NETHER_BRICK_FENCE) {
                    // 如果碰到墙壁，标记为碰到障碍物
                    hitObstacle = true;

                    // 播放碰撞音效
                    currentLoc.getWorld().playSound(currentLoc, Sound.BLOCK_STONE_BREAK, 1.0f, 0.8f);

                    // 显示碰撞粒子效果
                    currentLoc.getWorld().spawnParticle(Particle.LAVA,
                            currentLoc, 5, 0.2, 0.2, 0.2, 0.05);

                    return;
                }

                // 如果位置是空气或其他可替换的方块，替换为下界栅栏
                if (currentLoc.getBlock().getType() == Material.AIR
                        || currentLoc.getBlock().getType() == Material.CAVE_AIR
                        || currentLoc.getBlock().getType() == Material.SHORT_GRASS
                        || currentLoc.getBlock().getType() == Material.TALL_GRASS
                        || currentLoc.getBlock().getType() == Material.WATER) {

                    // 保存原始方块类型
                    originalBlocks.put(currentLoc.clone(), currentLoc.getBlock().getType());

                    // 替换为下界栅栏
                    currentLoc.getBlock().setType(Material.NETHER_BRICK_FENCE);
                    fenceLocations.add(currentLoc.clone());

                    // 显示粒子效果
                    currentLoc.getWorld().spawnParticle(Particle.FLAME,
                            currentLoc.clone().add(0.5, 0.5, 0.5), 5,
                            0.2, 0.2, 0.2, 0.05);

                    // 检测是否击中玩家
                    for (Entity entity : currentLoc.getWorld().getNearbyEntities(currentLoc, 1.0, 1.0, 1.0)) {
                        if (entity instanceof Player) {
                            Player hitPlayer = (Player) entity;

                            // 造成伤害
                            hitPlayer.damage(finalFenceDamage, mutationKing);

                            // 添加减速效果
                            hitPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, finalSlownessDuration, finalSlownessLevel));

                            // 播放受伤音效
                            hitPlayer.playSound(hitPlayer.getLocation(), Sound.ENTITY_PLAYER_HURT, 1.0f, 0.8f);

                            // 显示受伤粒子效果
                            hitPlayer.getWorld().spawnParticle(Particle.DAMAGE_INDICATOR,
                                    hitPlayer.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0.1);

                            if (debugMode) {
                                logger.info("IDC22下界栅栏射线" + (rayIndex + 1) + "命中玩家: " + hitPlayer.getName() +
                                           "，伤害: " + finalFenceDamage + "，减速" + (finalSlownessLevel + 1) + "级");
                            }
                        }
                    }
                }

                distance++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // 立即开始，每tick延伸一格
    }

    /**
     * 执行异变之王末影粒子减速场（基于原版MutationKing实现）
     */
    private void performMutationKingEnderField(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 简化实现：创建末影粒子减速场
        Location centerLoc = target.getLocation();

        // 播放音效
        mutationKing.getWorld().playSound(centerLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 2.0f, 0.5f);

        // 创建减速场任务
        new BukkitRunnable() {
            private int ticks = 0;
            private final int MAX_TICKS = 200; // 10秒

            @Override
            public void run() {
                if (ticks >= MAX_TICKS) {
                    this.cancel();
                    return;
                }

                // 显示粒子效果
                for (int i = 0; i < 20; i++) {
                    double angle = Math.random() * Math.PI * 2;
                    double radius = Math.random() * 10;
                    double x = Math.cos(angle) * radius;
                    double z = Math.sin(angle) * radius;
                    Location particleLoc = centerLoc.clone().add(x, Math.random() * 3, z);
                    centerLoc.getWorld().spawnParticle(Particle.PORTAL, particleLoc, 1, 0, 0, 0, 0);
                }

                // 对范围内玩家施加减速效果
                for (Entity entity : centerLoc.getWorld().getNearbyEntities(centerLoc, 10, 10, 10)) {
                    if (entity instanceof Player) {
                        Player player = (Player) entity;
                        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 3));
                    }
                }

                ticks += 10;
            }
        }.runTaskTimer(plugin, 0, 10); // 每0.5秒执行一次

        if (debugMode) {
            logger.info("IDC22异变之王发动末影粒子减速场，目标: " + target.getName());
        }
    }

    /**
     * 执行异变之王召唤变异生物（基于原版MutationKing实现）
     */
    private void performMutationKingSummon(org.bukkit.entity.EnderDragon mutationKing, Player target) {
        // 简化实现：随机召唤IDC1-IDC21
        Location summonLoc = target.getLocation().add(
            (Math.random() - 0.5) * 10,
            0,
            (Math.random() - 0.5) * 10
        );

        // 播放音效
        mutationKing.getWorld().playSound(summonLoc, Sound.ENTITY_ENDERMAN_TELEPORT, 1.5f, 0.8f);

        // 随机选择IDC1-IDC21
        int randomIdc = (int) (Math.random() * 21) + 1;
        String entityId = "idc" + randomIdc;

        // 尝试召唤实体
        try {
            LivingEntity summonedEntity = spawnUserCustomEntityDirect(summonLoc, entityId);
            if (summonedEntity != null) {
                // 显示召唤粒子效果
                summonLoc.getWorld().spawnParticle(Particle.PORTAL, summonLoc, 50, 1.0, 1.0, 1.0, 0.1);

                if (debugMode) {
                    logger.info("IDC22异变之王召唤了: " + entityId);
                }
            }
        } catch (Exception e) {
            if (debugMode) {
                logger.warning("IDC22召唤失败: " + entityId + " - " + e.getMessage());
            }
        }

        if (debugMode) {
            logger.info("IDC22异变之王发动召唤攻击，目标: " + target.getName());
        }
    }

    /**
     * 获取IDC22的配置参数
     */
    private EntityOverrideConfig getIDC22Config() {
        // 直接从configCache中获取IDC22的配置
        return configCache.get("idc22");
    }

    /**
     * 检查实体ID是否被支持（IDC1-IDC22）
     */
    public boolean isSupportedEntityId(String entityId) {
        return entityId != null && entityId.startsWith("idc") &&
               entityId.matches("idc([1-9]|1[0-9]|2[0-2])");
    }



    /**
     * 获取性能统计信息
     */
    public String getPerformanceStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("用户自定义实体系统性能统计:\n");
        stats.append("- 当前实体数量: ").append(currentUserCustomEntityCount).append("/").append(maxUserCustomEntities).append("\n");
        stats.append("- 配置缓存大小: ").append(configCache.size()).append("\n");
        stats.append("- 粒子效果任务数: ").append(particleEffectTasks.size()).append("\n");
        stats.append("- 上次生成时间: ").append(lastSpawnTime > 0 ? (System.currentTimeMillis() - lastSpawnTime) + "ms前" : "从未生成").append("\n");
        stats.append("- 配置缓存启用: ").append(enableConfigCache).append("\n");
        stats.append("- 性能监控启用: ").append(enablePerformanceMonitoring);
        return stats.toString();
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        // 取消所有粒子效果任务
        for (BukkitTask task : particleEffectTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        particleEffectTasks.clear();

        // 清理配置缓存（如果禁用缓存）
        if (!enableConfigCache) {
            configCache.clear();
        }

        if (debugMode) {
            logger.info("UserCustomEntity系统资源清理完成");
        }
    }
}
